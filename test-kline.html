<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K线图测试 - 真实币安数据</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .kline-container {
            width: 100%;
            height: 500px;
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .controls {
            background: #222;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            display: flex;
            gap: 10px;
            align-items: center;
        }
        
        .btn {
            background: #00d4aa;
            color: #000;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: bold;
        }
        
        .btn:hover {
            background: #00b894;
        }
        
        select {
            background: #333;
            color: #fff;
            border: 1px solid #555;
            padding: 8px;
            border-radius: 4px;
        }
        
        .status {
            background: #333;
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>📈 K线图测试 - 真实币安数据</h1>
        
        <div class="controls">
            <label>交易对:</label>
            <select id="symbolSelect">
                <option value="BTCUSDT">BTC/USDT</option>
                <option value="ETHUSDT">ETH/USDT</option>
                <option value="BNBUSDT">BNB/USDT</option>
                <option value="ADAUSDT">ADA/USDT</option>
            </select>
            
            <label>时间框架:</label>
            <select id="timeframeSelect">
                <option value="1m">1分钟</option>
                <option value="5m">5分钟</option>
                <option value="15m">15分钟</option>
                <option value="1h" selected>1小时</option>
                <option value="4h">4小时</option>
                <option value="1d">1天</option>
            </select>
            
            <button class="btn" onclick="loadKlineData()">加载数据</button>
            <button class="btn" onclick="testAPI()">测试API</button>
        </div>
        
        <div class="status" id="status">准备就绪...</div>
        
        <div class="kline-container" id="klineContainer">
            <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                点击"加载数据"开始
            </div>
        </div>
    </div>

    <script>
        // 状态显示
        function updateStatus(message) {
            const status = document.getElementById('status');
            const timestamp = new Date().toLocaleTimeString();
            status.textContent = `[${timestamp}] ${message}`;
            console.log(message);
        }
        
        // 测试API连接
        async function testAPI() {
            updateStatus('🔍 测试币安API连接...');
            
            try {
                const response = await fetch('/api/binance/klines?symbol=BTCUSDT&interval=1h&limit=5');
                const data = await response.json();
                
                if (data.success) {
                    updateStatus(`✅ API连接成功，获取到 ${data.data.length} 条K线数据`);
                    console.log('API数据示例:', data.data[0]);
                } else {
                    updateStatus(`❌ API连接失败: ${data.error}`);
                }
            } catch (error) {
                updateStatus(`❌ API测试失败: ${error.message}`);
            }
        }
        
        // 加载K线数据
        async function loadKlineData() {
            const symbol = document.getElementById('symbolSelect').value;
            const timeframe = document.getElementById('timeframeSelect').value;
            
            updateStatus(`📊 加载 ${symbol} ${timeframe} K线数据...`);
            
            try {
                const response = await fetch(`/api/binance/klines?symbol=${symbol}&interval=${timeframe}&limit=50`);
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error);
                }
                
                const klineData = data.data;
                updateStatus(`✅ 成功获取 ${klineData.length} 条K线数据`);
                
                // 绘制K线图
                drawKlineChart(klineData, symbol);
                
            } catch (error) {
                updateStatus(`❌ 数据加载失败: ${error.message}`);
            }
        }
        
        // 绘制K线图
        function drawKlineChart(klineData, symbol) {
            const container = document.getElementById('klineContainer');
            container.innerHTML = '';
            
            const canvas = document.createElement('canvas');
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            container.appendChild(canvas);
            
            const ctx = canvas.getContext('2d');
            
            // 转换数据格式
            const candleData = klineData.map(kline => ({
                open: parseFloat(kline[1]),
                high: parseFloat(kline[2]),
                low: parseFloat(kline[3]),
                close: parseFloat(kline[4]),
                volume: parseFloat(kline[5])
            }));
            
            // 计算价格范围
            const prices = candleData.flatMap(d => [d.high, d.low]);
            const minPrice = Math.min(...prices);
            const maxPrice = Math.max(...prices);
            const priceRange = maxPrice - minPrice;
            
            const width = canvas.width;
            const height = canvas.height;
            const padding = 60;
            const chartWidth = width - padding * 2;
            const chartHeight = height - padding * 2;
            
            // 清空画布
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(0, 0, width, height);
            
            // 绘制网格
            ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
            ctx.lineWidth = 1;
            
            // 水平网格线和价格标签
            for (let i = 0; i <= 5; i++) {
                const y = padding + (chartHeight / 5) * i;
                const price = maxPrice - (priceRange * i / 5);
                
                // 网格线
                ctx.beginPath();
                ctx.moveTo(padding, y);
                ctx.lineTo(width - padding, y);
                ctx.stroke();
                
                // 价格标签
                ctx.fillStyle = '#888';
                ctx.font = '12px Arial';
                ctx.textAlign = 'right';
                ctx.fillText('$' + price.toFixed(2), padding - 10, y + 4);
            }
            
            // 垂直网格线
            for (let i = 0; i <= 10; i++) {
                const x = padding + (chartWidth / 10) * i;
                ctx.beginPath();
                ctx.moveTo(x, padding);
                ctx.lineTo(x, height - padding);
                ctx.stroke();
            }
            
            // 绘制K线
            const candleWidth = chartWidth / candleData.length * 0.8;
            const candleSpacing = chartWidth / candleData.length;
            
            candleData.forEach((candle, index) => {
                const x = padding + index * candleSpacing + candleSpacing / 2;
                
                // 计算Y坐标
                const openY = padding + (maxPrice - candle.open) / priceRange * chartHeight;
                const highY = padding + (maxPrice - candle.high) / priceRange * chartHeight;
                const lowY = padding + (maxPrice - candle.low) / priceRange * chartHeight;
                const closeY = padding + (maxPrice - candle.close) / priceRange * chartHeight;
                
                // 确定颜色
                const isRising = candle.close > candle.open;
                const color = isRising ? '#00d4aa' : '#ff6b6b';
                
                // 绘制影线
                ctx.strokeStyle = color;
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(x, highY);
                ctx.lineTo(x, lowY);
                ctx.stroke();
                
                // 绘制实体
                ctx.fillStyle = color;
                const bodyTop = Math.min(openY, closeY);
                const bodyHeight = Math.abs(closeY - openY);
                ctx.fillRect(x - candleWidth / 2, bodyTop, candleWidth, Math.max(bodyHeight, 1));
            });
            
            // 绘制标题
            ctx.fillStyle = '#00d4aa';
            ctx.font = 'bold 16px Arial';
            ctx.textAlign = 'left';
            ctx.fillText(`📈 ${symbol} K线图 (真实币安数据)`, padding, 30);
            
            // 绘制当前价格
            const currentPrice = candleData[candleData.length - 1].close;
            ctx.fillStyle = '#ffd700';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'right';
            ctx.fillText(`当前价格: $${currentPrice.toFixed(2)}`, width - padding, 30);
            
            updateStatus(`✅ K线图绘制完成 - 价格范围: $${minPrice.toFixed(2)} - $${maxPrice.toFixed(2)}`);
        }
        
        // 页面加载完成后自动测试API
        document.addEventListener('DOMContentLoaded', function() {
            updateStatus('📄 页面加载完成');
            setTimeout(testAPI, 1000);
        });
    </script>
</body>
</html>
