/* 断金ใ现货'杠杆自动化交易系统 - 主样式文件 */
/* 完全响应式设计，支持桌面、平板、手机所有设备 */

/* 基础样式 - 完全匹配原版 */
body {
    background: #000;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
    font-size: 12px; /* 缩小基础字体 */
    background-image:
        radial-gradient(circle at 20% 50%, rgba(0, 255, 136, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 20%, rgba(0, 136, 255, 0.1) 0%, transparent 50%);
}

/* 矩阵背景动画 - 黑客帝国风格！ */
.matrix-bg {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    opacity: 0.95; /* 提高透明度让代码雨更突出 */
    z-index: -1;
    background: linear-gradient(135deg, #000000 0%, #001a00 30%, #000000 70%, #001100 100%);
}

/* 主容器 - 匹配原版布局 */
.main-container {
    display: flex;
    height: 100vh;
}

/* 交易区域 */
.trading-section {
    flex: 1;
    padding: 16px;
}

/* 横向设置栏样式 - 进一步缩小占用空间 */
.horizontal-settings-bar {
    margin-bottom: 8px;
    padding: 6px 10px;
    background: rgba(0, 212, 170, 0.1);
    border-radius: 6px;
    border: 1px solid rgba(0, 212, 170, 0.3);
}

/* 设置行样式 - 更紧凑布局节省空间 */
.settings-row {
    display: flex;
    justify-content: space-evenly;
    align-items: center;
    gap: 6px;
    margin-bottom: 4px;
    flex-wrap: nowrap;
}

.settings-row:last-child {
    margin-bottom: 0;
}

/* 设置组样式 - 更紧凑布局 */
.setting-group {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    min-width: 80px;
    flex: 1;
    max-width: 130px;
}

.setting-group label {
    font-size: 9px;
    color: #00d4aa;
    font-weight: 600;
    text-align: center;
    white-space: nowrap;
}

.control-buttons {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* 状态指示灯样式 */
.status-indicator {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.status-light {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    transition: all 0.3s ease;
}

.status-light.connected {
    background: #10b981;
    box-shadow: 0 0 8px rgba(16, 185, 129, 0.6);
}

.status-light.disconnected {
    background: #ef4444;
    box-shadow: 0 0 8px rgba(239, 68, 68, 0.6);
}

.status-light::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 6px;
    height: 6px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.8);
}

.status-text {
    font-size: 9px;
    color: #9ca3af;
    text-align: center;
}

/* 登录按钮样式 */
.login-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 8px 16px;
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #e5e7eb;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 80px;
    justify-content: center;
}

.login-btn:hover {
    background: rgba(30, 41, 59, 1);
    border-color: rgba(255, 255, 255, 0.3);
    transform: translateY(-1px);
}

.login-btn.connected {
    background: rgba(16, 185, 129, 0.2);
    border-color: rgba(16, 185, 129, 0.5);
    color: #10b981;
}

.login-btn.connected:hover {
    background: rgba(16, 185, 129, 0.3);
}

.platform-btn {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    font-size: 10px;
    font-weight: bold;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    line-height: 1.2;
    position: relative;
    transform-style: preserve-3d;
}

/* 币安现货按钮 - 绿色激活状态 */
.spot-btn {
    background: linear-gradient(145deg, #10b981, #059669);
    color: white;
    box-shadow:
        0 8px 25px rgba(16, 185, 129, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.spot-btn:hover {
    background: linear-gradient(145deg, #059669, #047857);
    transform: translateY(-2px);
    box-shadow:
        0 12px 30px rgba(16, 185, 129, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.spot-btn:active {
    transform: translateY(0px);
    box-shadow:
        0 4px 15px rgba(16, 185, 129, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

/* TC杠杆按钮 - 红色未激活状态 */
.leverage-btn {
    background: linear-gradient(145deg, #6b7280, #4b5563);
    color: #9ca3af;
    box-shadow:
        0 8px 25px rgba(107, 114, 128, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.leverage-btn:hover {
    background: linear-gradient(145deg, #4b5563, #374151);
    transform: translateY(-2px);
    box-shadow:
        0 12px 30px rgba(107, 114, 128, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.leverage-btn:active {
    transform: translateY(0px);
    box-shadow:
        0 4px 15px rgba(107, 114, 128, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

/* 激活状态的TC杠杆按钮 - 红色 */
.leverage-btn.active {
    background: linear-gradient(145deg, #ef4444, #dc2626);
    color: white;
    box-shadow:
        0 8px 25px rgba(239, 68, 68, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.leverage-btn.active:hover {
    background: linear-gradient(145deg, #dc2626, #b91c1c);
    transform: translateY(-2px);
    box-shadow:
        0 12px 30px rgba(239, 68, 68, 0.5),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.leverage-btn.active:active {
    transform: translateY(0px);
    box-shadow:
        0 4px 15px rgba(239, 68, 68, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

/* 未激活状态的币安现货按钮 - 灰色 */
.spot-btn.inactive {
    background: linear-gradient(145deg, #6b7280, #4b5563);
    color: #9ca3af;
    box-shadow:
        0 8px 25px rgba(107, 114, 128, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.spot-btn.inactive:hover {
    background: linear-gradient(145deg, #4b5563, #374151);
    transform: translateY(-2px);
    box-shadow:
        0 12px 30px rgba(107, 114, 128, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.1),
        inset 0 -1px 0 rgba(0, 0, 0, 0.2);
}

.emergency-btn {
    background: #dc2626;
    color: white;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    font-size: 12px;
    font-weight: bold;
    border: none;
    cursor: pointer;
    animation: pulse 2s infinite;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
}

.emergency-btn:hover {
    background: #b91c1c;
}

/* 交易网格 - 方块网格布局 */
.trading-grid {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    gap: 8px;
    margin-bottom: 16px;
}

/* 加密货币卡片 - 方块网格布局 */
.crypto-item {
    background: rgba(0, 20, 40, 0.9);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 0px;
    padding: 10px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    min-height: 90px;
    width: 100%;
    text-align: center;
}

.crypto-item:hover {
    border-color: #00ff88;
    box-shadow: 0 0 15px rgba(0, 255, 136, 0.3);
}

/* 币种信息区域 - 方块布局居中 */
.crypto-info {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin-bottom: 8px;
}

.crypto-symbol {
    font-size: 13px;
    font-weight: bold;
    margin-bottom: 3px;
    color: #00ff88;
}

.crypto-name {
    font-size: 9px;
    color: #9ca3af;
    margin-bottom: 0;
}

/* 价格区域 - 方块布局居中 */
.crypto-price-section {
    display: flex;
    flex-direction: column;
    align-items: center;
    width: 100%;
    margin-bottom: 8px;
}

.crypto-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    margin-bottom: 4px;
}

/* 止盈止损控制区域 */
.profit-loss-controls {
    display: flex;
    gap: 3px;
    margin-bottom: 4px;
}

.profit-select,
.loss-select {
    flex: 1;
    background: rgba(0, 20, 40, 0.8);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 3px;
    color: white;
    font-size: 8px;
    padding: 1px 3px;
    cursor: pointer;
    transition: all 0.2s ease;
    height: 16px;
}

.profit-select {
    border-color: rgba(16, 185, 129, 0.5);
}

.profit-select:hover,
.profit-select:focus {
    border-color: #10b981;
    background: rgba(16, 185, 129, 0.1);
    outline: none;
}

.loss-select {
    border-color: rgba(239, 68, 68, 0.5);
}

.loss-select:hover,
.loss-select:focus {
    border-color: #ef4444;
    background: rgba(239, 68, 68, 0.1);
    outline: none;
}

.profit-select option,
.loss-select option {
    background: rgba(0, 20, 40, 0.95);
    color: white;
    font-size: 9px;
}

.leverage-btn-small {
    background: rgba(0, 255, 136, 0.2);
    border: 1px solid #00ff88;
    color: #00ff88;
    padding: 2px 6px;
    border-radius: 3px;
    font-size: 10px;
    margin: 0 2px;
    cursor: pointer;
}

.all-button-small {
    background: rgba(255, 193, 7, 0.2);
    border: 1px solid #ffc107;
    color: #ffc107;
    padding: 1px 4px;
    border-radius: 2px;
    font-size: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    margin-left: 2px;
}

.all-button-small:hover {
    background: rgba(255, 193, 7, 0.3);
    transform: scale(1.05);
}

/* 金额输入框样式 */
.crypto-amount-input {
    margin: 4px 0;
}

.amount-input {
    width: 100%;
    background: transparent;
    border: 1px solid rgba(0, 255, 136, 0.4);
    border-radius: 3px;
    padding: 4px 6px;
    color: #00ff88;
    font-size: 11px;
    text-align: center;
    transition: all 0.3s ease;
}

.amount-input:focus {
    outline: none;
    border-color: #00ff88;
    box-shadow: 0 0 5px rgba(0, 255, 136, 0.3);
}

.amount-input::placeholder {
    color: rgba(0, 255, 136, 0.5);
}

.crypto-price {
    font-size: 11px; /* 缩小字体 */
    font-weight: bold;
    margin-bottom: 3px; /* 缩小间距 */
}

.crypto-change {
    font-size: 12px;
    margin-bottom: 8px;
}

.price-up { color: #00ff88; }
.price-down { color: #ff4757; }

.crypto-actions {
    display: flex;
    gap: 4px;
    width: 100%;
    justify-content: center;
}

.buy-btn {
    background: #00ff88;
    color: #000;
    border: none;
    padding: 3px 8px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 10px;
    cursor: pointer;
    flex: 1;
}

.sell-btn {
    background: #ff4757;
    color: white;
    border: none;
    padding: 3px 8px;
    border-radius: 3px;
    font-weight: bold;
    font-size: 10px;
    cursor: pointer;
    flex: 1;
}

/* 交易记录 - 紧凑但可用 */
.trade-history {
    background: rgba(0, 20, 40, 0.8);
    border: 1px solid rgba(0, 255, 136, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 8px;
    height: 180px;
    display: flex;
    flex-direction: column;
}

.table-container {
    flex: 1;
    overflow-y: auto;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.section-title {
    font-size: 14px;
    font-weight: bold;
}

.update-indicator {
    font-size: 14px;
    color: #9ca3af;
}

.table-container {
    overflow-x: auto;
}

.trade-table {
    width: 100%;
    font-size: 12px;
    line-height: 1.3;
}

.table-header {
    border-bottom: 1px solid #374151;
}

.table-cell {
    text-align: left;
    padding: 6px 4px;
    vertical-align: middle;
}

/* 侧边栏 - 缩小版本 */
.sidebar {
    width: 280px; /* 缩小宽度 */
    padding: 12px; /* 缩小内边距 */
    border-left: 1px solid #374151;
}

/* 余额头部布局 */
.balance-header-top {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
}

/* 账户切换标签 */
.account-switch-tabs {
    display: flex;
    gap: 6px;
}

/* 余额控制按钮组 */
.balance-controls {
    display: flex;
    gap: 6px;
    align-items: center;
}

.balance-control-btn {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    color: #fff;
    width: 32px;
    height: 32px;
    border-radius: 6px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    transition: all 0.3s ease;
    position: relative;
}

.balance-control-btn:hover {
    background: rgba(0, 212, 170, 0.2);
    border-color: #00d4aa;
    transform: translateY(-1px);
}

.balance-control-btn:active {
    transform: translateY(0);
}

.balance-control-btn.refreshing {
    animation: spin 1s linear infinite;
}

/* 隐藏余额状态 */
.balance-hidden .balance-amount,
.balance-hidden .balance-item-value {
    color: transparent;
    text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
    user-select: none;
}

.balance-hidden .balance-amount::after,
.balance-hidden .balance-item-value::after {
    content: '****';
    color: #888;
    text-shadow: none;
    position: absolute;
    left: 50%;
    transform: translateX(-50%);
}

.account-tab {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid rgba(0, 212, 170, 0.3);
    background: rgba(0, 20, 40, 0.8);
    color: #d1d4dc;
    border-radius: 8px;
    cursor: pointer;
    font-size: 13px;
    font-weight: 600;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    text-align: center;
}

.account-tab:hover {
    background: rgba(0, 212, 170, 0.1);
    border-color: rgba(0, 212, 170, 0.5);
    transform: translateY(-1px);
}

.account-tab.active {
    background: rgba(0, 212, 170, 0.2);
    border-color: #00d4aa;
    color: #00d4aa;
    box-shadow: 0 0 15px rgba(0, 212, 170, 0.4);
}

/* 余额卡片 - 缩小版本 */
.balance-card {
    background: rgba(0, 20, 40, 0.9);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 8px; /* 缩小圆角 */
    padding: 14px; /* 缩小内边距 */
    margin-bottom: 12px; /* 缩小底部边距 */
    transition: all 0.3s ease;
}

.balance-card:hover {
    border-color: rgba(0, 255, 136, 0.5);
    box-shadow: 0 6px 20px rgba(0, 255, 136, 0.15);
}

/* 现货账户特殊样式 */
.balance-card.spot-balance {
    border-color: rgba(34, 197, 94, 0.3);
}

.balance-card.spot-balance:hover {
    border-color: rgba(34, 197, 94, 0.5);
    box-shadow: 0 6px 20px rgba(34, 197, 94, 0.15);
}

/* 杠杆账户特殊样式 */
.balance-card.margin-balance {
    border-color: rgba(251, 191, 36, 0.3);
}

.balance-card.margin-balance:hover {
    border-color: rgba(251, 191, 36, 0.5);
    box-shadow: 0 6px 20px rgba(251, 191, 36, 0.15);
}

.balance-header {
    text-align: center;
    margin-bottom: 16px;
}

.balance-label {
    font-size: 14px;
    color: #9ca3af;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 账户类型图标 */
.account-type-icon {
    margin-right: 6px;
    font-size: 14px;
}

/* 风险等级样式 */
.risk-level {
    font-weight: bold;
}

.risk-level.safe {
    color: #10b981;
}

.risk-level.warning {
    color: #f59e0b;
}

.risk-level.danger {
    color: #ef4444;
}

/* 已借资产样式 */
.borrowed {
    color: #f59e0b;
}

.balance-amount {
    font-size: 24px; /* 缩小字体 */
    font-weight: bold;
    color: #10b981;
}

.balance-change {
    font-size: 11px; /* 缩小字体 */
    color: #10b981;
}

.balance-details {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.balance-item {
    display: flex;
    justify-content: space-between;
}

.balance-item-label {
    color: #9ca3af;
}

.balance-item-value {
    color: white;
    font-weight: bold;
}

.balance-item-value.frozen {
    color: #fbbf24;
}

.balance-item-value.profit {
    color: #10b981;
}

/* 设置面板 - 匹配原版 */
.settings-panel, .position-panel {
    background: rgba(0, 20, 40, 0.8);
    border: 1px solid rgba(0, 255, 136, 0.2);
    backdrop-filter: blur(10px);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 24px;
}

/* 持仓分布面板特殊样式 - 适中高度 */
.position-panel {
    margin-top: 16px;
    min-height: 300px;
    max-height: 450px;
    overflow-y: auto;
    margin-bottom: 12px;
    flex-grow: 1;
}

.panel-title {
    font-size: 18px;
    font-weight: bold;
    margin-bottom: 16px;
}

.settings-content {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

/* API连接面板样式 */
.api-connection-panel {
    background: rgba(0, 30, 60, 0.6);
    border: 1px solid rgba(0, 255, 136, 0.3);
    border-radius: 8px;
    padding: 16px;
    margin-bottom: 20px;
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.connection-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
}

.connection-info {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.connection-label {
    font-size: 14px;
    font-weight: bold;
    color: #e5e7eb;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-light {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    box-shadow: 0 0 8px currentColor;
    animation: pulse 2s infinite;
}

.status-light.red {
    background: #dc2626;
    color: #dc2626;
}

.status-light.green {
    background: #10b981;
    color: #10b981;
}

.status-text {
    font-size: 12px;
    color: #9ca3af;
}

.connection-btn {
    background: linear-gradient(135deg, #059669, #047857);
    color: white;
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-size: 12px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    min-width: 60px;
}

.connection-btn:hover {
    background: linear-gradient(135deg, #047857, #065f46);
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(5, 150, 105, 0.3);
}

.connection-btn:active {
    transform: translateY(0);
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.setting-item {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.setting-label {
    font-size: 14px;
    color: #9ca3af;
    margin-bottom: 8px;
}

.toggle-btn {
    width: 100%;
    padding: 8px;
    border-radius: 6px;
    font-weight: bold;
    border: none;
    cursor: pointer;
}

.toggle-btn.active {
    background: #059669;
    color: white;
}

.toggle-btn.inactive {
    background: #dc2626;
    color: white;
}

.setting-select {
    width: 100%;
    background: rgba(31, 41, 55, 0.8);
    border: 1px solid rgba(0, 212, 170, 0.3);
    border-radius: 6px;
    padding: 6px 8px;
    color: white;
    font-size: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.setting-select:hover {
    border-color: #00d4aa;
    background: rgba(31, 41, 55, 0.9);
}

.setting-select:focus {
    outline: none;
    border-color: #00d4aa;
    box-shadow: 0 0 0 2px rgba(0, 212, 170, 0.2);
}

.risk-controls {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.risk-item {
    display: flex;
    justify-content: space-between;
}

.risk-label {
    font-size: 14px;
}

.risk-value.loss {
    color: #ef4444;
}

.risk-value.profit {
    color: #10b981;
}

/* 持仓分布样式 - 紧凑布局 */
.position-chart {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 12px 0;
    min-height: 250px;
}

.position-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 0;
    border-bottom: 1px solid rgba(0, 255, 136, 0.1);
}

.position-info {
    display: flex;
    align-items: center;
    gap: 8px;
}

.position-indicator {
    width: 12px;
    height: 12px;
    background: #10b981;
    border-radius: 50%;
}

.position-symbol {
    font-size: 14px;
    font-weight: bold;
}

.position-details {
    text-align: right;
}

.position-percentage {
    font-size: 14px;
    font-weight: bold;
}

.position-value {
    font-size: 12px;
    color: #9ca3af;
}

/* 响应式设计 - 支持所有设备 */

/* 平板设备 (1024px 及以下) */
@media (max-width: 1024px) {
    .main-container {
        padding: 12px;
        gap: 12px;
    }

    .sidebar {
        width: 280px;
    }

    .trading-grid {
        grid-template-columns: repeat(5, 1fr);
        gap: 6px;
    }

    .crypto-item {
        padding: 8px;
    }

    .crypto-symbol {
        font-size: 11px;
    }

    .crypto-name {
        font-size: 9px;
    }

    .crypto-price {
        font-size: 12px;
    }

    .crypto-change {
        font-size: 10px;
    }

    .buy-btn, .sell-btn {
        font-size: 10px;
        padding: 3px 8px;
    }

    .all-button-small {
        font-size: 7px;
        padding: 1px 3px;
    }

    .amount-input {
        font-size: 9px;
        padding: 2px 4px;
    }

    .leverage-btn-small {
        font-size: 8px;
        padding: 1px 4px;
    }
}

/* 移动设备 (768px 及以下) */
@media (max-width: 768px) {
    .main-container {
        flex-direction: column;
        padding: 8px;
        gap: 16px;
        min-height: auto;
    }

    .sidebar {
        width: 100%;
        order: -1; /* 将侧边栏移到顶部 */
        border-left: none;
        border-bottom: 1px solid #374151;
        padding-bottom: 16px;
    }

    .control-bar {
        flex-direction: column;
        align-items: stretch;
        gap: 12px;
    }

    .control-buttons {
        justify-content: center;
    }

    .emergency-btn {
        align-self: center;
        width: 45px;
        height: 45px;
        font-size: 11px;
    }

    .trading-grid {
        grid-template-columns: repeat(4, 1fr);
        gap: 4px;
    }

    .crypto-item {
        padding: 6px;
    }

    .crypto-symbol {
        font-size: 10px;
    }

    .crypto-name {
        font-size: 8px;
    }

    .crypto-price {
        font-size: 11px;
    }

    .crypto-change {
        font-size: 9px;
    }

    .buy-btn, .sell-btn {
        font-size: 9px;
        padding: 2px 6px;
    }

    .all-button-small {
        font-size: 6px;
        padding: 1px 2px;
    }

    .amount-input {
        font-size: 8px;
        padding: 2px 3px;
    }

    .leverage-btn-small {
        font-size: 7px;
        padding: 1px 3px;
    }

    .balance-card {
        padding: 16px;
    }

    .balance-amount {
        font-size: 24px;
    }

    .settings-panel, .position-panel {
        padding: 12px;
    }

    .trade-history {
        padding: 12px;
    }

    .trade-table {
        font-size: 11px;
        line-height: 1.2;
    }

    .table-cell {
        padding: 5px 3px;
    }
}

/* 小屏手机 (480px 及以下) */
@media (max-width: 480px) {
    .main-container {
        padding: 4px;
    }

    .trading-grid {
        grid-template-columns: repeat(3, 1fr);
        gap: 3px;
    }

    .crypto-item {
        padding: 4px;
    }

    .crypto-symbol {
        font-size: 9px;
    }

    .crypto-name {
        font-size: 7px;
        margin-bottom: 4px;
    }

    .crypto-price {
        font-size: 10px;
    }

    .crypto-change {
        font-size: 8px;
    }

    .buy-btn, .sell-btn {
        font-size: 8px;
        padding: 2px 4px;
    }

    .all-button-small {
        font-size: 6px;
        padding: 1px 2px;
    }

    .amount-input {
        font-size: 7px;
        padding: 1px 2px;
    }

    .leverage-btn-small {
        font-size: 6px;
        padding: 1px 2px;
    }

    .balance-card {
        padding: 12px;
    }

    .balance-amount {
        font-size: 20px;
    }

    .balance-change {
        font-size: 12px;
    }

    .settings-panel, .position-panel {
        padding: 8px;
    }

    .panel-title {
        font-size: 16px;
    }

    .trade-history {
        padding: 8px;
    }

    .section-title {
        font-size: 16px;
    }

    .trade-table {
        font-size: 10px;
        line-height: 1.1;
    }

    .table-cell {
        padding: 4px 2px;
    }

    .platform-btn {
        width: 50px;
        height: 50px;
        font-size: 9px;
    }

    .emergency-btn {
        width: 40px;
        height: 40px;
        font-size: 10px;
    }
}

/* 超小屏设备 (360px 及以下) */
@media (max-width: 360px) {
    .trading-grid {
        grid-template-columns: repeat(2, 1fr);
        gap: 2px;
    }

    .crypto-item {
        padding: 3px;
    }

    .crypto-symbol {
        font-size: 8px;
    }

    .crypto-name {
        font-size: 6px;
    }

    .crypto-price {
        font-size: 9px;
    }

    .crypto-change {
        font-size: 7px;
    }

    .buy-btn, .sell-btn {
        font-size: 7px;
        padding: 1px 3px;
    }

    .all-button-small {
        font-size: 5px;
        padding: 1px 2px;
    }

    .amount-input {
        font-size: 6px;
        padding: 1px 2px;
    }

    .balance-amount {
        font-size: 18px;
    }
}

/* 交易类型切换标签 */
.trade-type-tabs {
    display: flex;
    gap: 8px;
    margin-left: auto;
    margin-right: 16px;
}

.trade-tab {
    padding: 6px 12px;
    border: 1px solid rgba(0, 212, 170, 0.3);
    background: rgba(0, 20, 40, 0.8);
    color: #d1d4dc;
    border-radius: 6px;
    cursor: pointer;
    font-size: 12px;
    font-weight: 500;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
}

.trade-tab:hover {
    background: rgba(0, 212, 170, 0.1);
    border-color: rgba(0, 212, 170, 0.5);
    transform: translateY(-1px);
}

.trade-tab.active {
    background: rgba(0, 212, 170, 0.2);
    border-color: #00d4aa;
    color: #00d4aa;
    box-shadow: 0 0 10px rgba(0, 212, 170, 0.3);
}

/* 分页控制样式 */
.pagination-controls {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 4px;
    margin-top: 8px;
    padding-top: 8px;
    border-top: 1px solid rgba(0, 255, 136, 0.1);
}

.page-btn {
    background: rgba(0, 255, 136, 0.1);
    border: 1px solid rgba(0, 255, 136, 0.3);
    color: rgba(255, 255, 255, 0.7);
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.page-btn:hover {
    background: rgba(0, 255, 136, 0.2);
    color: #00ff88;
    border-color: #00ff88;
}

.page-btn.active {
    background: rgba(0, 255, 136, 0.3);
    color: #00ff88;
    border-color: #00ff88;
    font-weight: bold;
}

.page-btn:disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.page-numbers {
    display: flex;
    gap: 2px;
}



/* 版权信息样式 - 功臣们要有排面！ */
.copyright-info {
    position: fixed;
    bottom: 50px;
    right: 20px;
    z-index: 1000;
    pointer-events: none;
}

.copyright-text {
    font-size: 13px;
    color: rgba(255, 255, 255, 0.7);
    text-align: right;
    line-height: 1.4;
    font-family: 'SF Pro Display', -apple-system, BlinkMacSystemFont, sans-serif;
    text-shadow: 0 0 5px rgba(0, 0, 0, 0.9);
    font-weight: 500;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.modal-content {
    background: linear-gradient(135deg, rgba(15, 23, 42, 0.95), rgba(30, 41, 59, 0.95));
    margin: 10% auto;
    padding: 0;
    border: 1px solid rgba(0, 255, 0, 0.3);
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    box-shadow: 0 20px 60px rgba(0, 255, 0, 0.2);
    animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20px 25px;
    border-bottom: 1px solid rgba(0, 255, 0, 0.2);
}

.modal-header h3 {
    margin: 0;
    color: #00ff00;
    font-size: 18px;
    font-weight: 600;
}

.close {
    color: #9ca3af;
    font-size: 28px;
    font-weight: bold;
    cursor: pointer;
    transition: color 0.3s ease;
}

.close:hover {
    color: #00ff00;
}

.modal-body {
    padding: 25px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    color: #e5e7eb;
    font-size: 14px;
    font-weight: 500;
}

.form-group input[type="text"],
.form-group input[type="password"] {
    width: 100%;
    padding: 12px 15px;
    background: rgba(30, 41, 59, 0.8);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 8px;
    color: #e5e7eb;
    font-size: 14px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.form-group input[type="text"]:focus,
.form-group input[type="password"]:focus {
    outline: none;
    border-color: #00ff00;
    box-shadow: 0 0 0 2px rgba(0, 255, 0, 0.2);
}

.form-group input[type="checkbox"] {
    margin-right: 8px;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: flex-end;
    margin-top: 30px;
}

.btn-cancel,
.btn-confirm {
    padding: 12px 24px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-cancel {
    background: rgba(107, 114, 128, 0.8);
    color: #e5e7eb;
}

.btn-cancel:hover {
    background: rgba(107, 114, 128, 1);
}

.btn-confirm {
    background: linear-gradient(135deg, #00ff00, #00cc00);
    color: #000;
}

.btn-confirm:hover {
    background: linear-gradient(135deg, #00cc00, #009900);
    transform: translateY(-1px);
}

/* ==================== K线图样式 ==================== */

/* K线图面板 - 优化空间 */
.kline-panel {
    background: rgba(0, 20, 40, 0.95);
    border-radius: 8px; /* 减小圆角 */
    border: 1px solid rgba(0, 212, 170, 0.3);
    margin-bottom: 12px; /* 减小边距 */
    overflow: hidden;
    backdrop-filter: blur(10px);
    flex: 1; /* 自动填充可用空间 */
    min-height: 500px; /* 设置最小高度 */
}

/* K线图头部 - 紧凑设计 */
.kline-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px; /* 减小内边距 */
    background: rgba(0, 212, 170, 0.1);
    border-bottom: 1px solid rgba(0, 212, 170, 0.2);
}

.kline-header .panel-title {
    margin: 0;
    font-size: 14px;
    font-weight: 600;
    color: #00d4aa;
}

/* K线图控制区域 */
.kline-controls {
    display: flex;
    gap: 8px;
    align-items: center;
}

.kline-select {
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid rgba(0, 212, 170, 0.3);
    border-radius: 6px;
    color: #fff;
    padding: 4px 8px;
    font-size: 11px;
    min-width: 80px;
}

.kline-select:focus {
    outline: none;
    border-color: #00d4aa;
    box-shadow: 0 0 0 2px rgba(0, 212, 170, 0.2);
}

.kline-btn {
    background: rgba(0, 212, 170, 0.2);
    border: 1px solid rgba(0, 212, 170, 0.3);
    border-radius: 6px;
    color: #00d4aa;
    padding: 4px 8px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.kline-btn:hover {
    background: rgba(0, 212, 170, 0.3);
    transform: translateY(-1px);
}

/* K线图容器 - 优化空间利用 */
.kline-chart-container {
    position: relative;
    height: 400px; /* 增加高度 */
    background: #1a1a1a;
    overflow: hidden;
    margin: 0; /* 移除边距 */
    border-radius: 0; /* 移除圆角以节省空间 */
}

/* 加载状态 */
.kline-loading {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    text-align: center;
    color: #00d4aa;
}

.loading-spinner {
    width: 32px;
    height: 32px;
    border: 3px solid rgba(0, 212, 170, 0.3);
    border-top: 3px solid #00d4aa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin: 0 auto 8px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-text {
    font-size: 12px;
    color: rgba(255, 255, 255, 0.7);
}

/* 技术指标信息 - 紧凑布局 */
.kline-indicators {
    display: flex;
    justify-content: space-between;
    padding: 8px 12px; /* 减小内边距 */
    background: rgba(0, 0, 0, 0.3);
    border-top: 1px solid rgba(0, 212, 170, 0.2);
}

.indicator-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    flex: 1;
}

.indicator-label {
    font-size: 10px;
    color: rgba(255, 255, 255, 0.6);
    margin-bottom: 4px;
}

.indicator-value {
    font-size: 11px;
    font-weight: 600;
}

.indicator-value.support {
    color: #26a69a;
}

.indicator-value.resistance {
    color: #ef5350;
}

.indicator-value.suggestion {
    color: #00d4aa;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .kline-chart-container {
        height: 250px;
    }

    .kline-controls {
        flex-wrap: wrap;
        gap: 4px;
    }

    .kline-select {
        min-width: 70px;
        font-size: 10px;
    }

    .kline-indicators {
        flex-direction: column;
        gap: 8px;
    }

    .indicator-item {
        flex-direction: row;
        justify-content: space-between;
    }
}

/* 全屏模式样式 */
.kline-fullscreen {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 9999;
    background: #000;
}

.kline-fullscreen .kline-chart-container {
    height: calc(100vh - 120px);
}
