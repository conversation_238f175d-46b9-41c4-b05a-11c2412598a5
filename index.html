<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>断金ใ现货'杠杆自动化交易系统</title>
    <script src="https://cdn.tailwindcss.com"></script>
    <link rel="stylesheet" href="styles/main.css">
</head>
<body class="min-h-screen text-white">
    <!-- 简单乱码雨背景 -->
    <canvas class="matrix-bg" id="canvas"></canvas>
    
    <!-- 主容器 - 响应式布局 -->
    <div class="main-container">
        <!-- 左侧交易区域 -->
        <div class="trading-section">
            <!-- 顶部交易设置栏 - 横向布局 -->
            <div class="horizontal-settings-bar">
                <!-- 第一行：API连接和自动交易 -->
                <div class="settings-row">
                    <div class="setting-group">
                        <label>币安API</label>
                        <button class="login-btn" id="binanceLoginBtn" onclick="toggleBinanceConnection()">
                            <span class="status-light disconnected" id="binanceStatusLight"></span>
                            <span id="binanceStatusText">登录</span>
                        </button>
                    </div>

                    <div class="setting-group">
                        <label>TG机器人</label>
                        <button class="login-btn" id="telegramLoginBtn" onclick="toggleTelegramConnection()">
                            <span class="status-light disconnected" id="telegramStatusLight"></span>
                            <span id="telegramStatusText">登录</span>
                        </button>
                    </div>

                    <div class="setting-group">
                        <label>自动交易</label>
                        <button class="toggle-btn active" id="autoTradeToggle" onclick="toggleAutoTrade()">已启用</button>
                    </div>

                    <!-- 紧急停止按钮 -->
                    <button class="emergency-btn">
                        紧急停止
                    </button>
                </div>

                <!-- 第二行：交易参数设置 -->
                <div class="settings-row">
                    <div class="setting-group">
                        <label>杠杆倍数</label>
                        <select class="setting-select" id="leverageSelect">
                            <option value="1">1x (现货)</option>
                            <option value="3">3x</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label>买入策略</label>
                        <select class="setting-select" id="buyStrategySelect">
                            <option value="grid">网格交易</option>
                            <option value="dca">定投策略</option>
                            <option value="momentum">动量交易</option>
                            <option value="mean_reversion">均值回归</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label>卖出策略</label>
                        <select class="setting-select" id="sellStrategySelect">
                            <option value="stop_loss">止盈止损</option>
                            <option value="trailing">追踪止损</option>
                            <option value="grid">网格卖出</option>
                            <option value="time_based">定时卖出</option>
                        </select>
                    </div>

                    <div class="setting-group">
                        <label>周期设定</label>
                        <select class="setting-select" id="periodSelect">
                            <option value="1m">1分钟</option>
                            <option value="5m">5分钟</option>
                            <option value="15m">15分钟</option>
                            <option value="1h">1小时</option>
                            <option value="4h">4小时</option>
                            <option value="1d">1天</option>
                        </select>
                    </div>
                </div>
            </div>

            <!-- 加密货币交易网格 - 动态生成21种货币 -->
            <div class="trading-grid" id="cryptoGrid">
                <!-- 通过JavaScript动态生成加密货币卡片 -->
            </div>

            <!-- 交易记录表格 - 分为现货和杠杆 -->
            <div class="trade-history">
                <div class="section-header">
                    <h3 class="section-title">交易记录</h3>
                    <div class="trade-type-tabs">
                        <button class="trade-tab active" id="spotTradeTab" onclick="switchTradeType('spot')">
                            🔵 现货交易
                        </button>
                        <button class="trade-tab" id="marginTradeTab" onclick="switchTradeType('margin')">
                            🔴 杠杆全仓
                        </button>
                    </div>
                    <div class="update-indicator">实时更新</div>
                </div>
                <div class="table-container">
                    <table class="trade-table">
                        <thead>
                            <tr class="table-header">
                                <th class="table-cell">时间</th>
                                <th class="table-cell">交易对</th>
                                <th class="table-cell">类型</th>
                                <th class="table-cell">数量</th>
                                <th class="table-cell">价格</th>
                                <th class="table-cell">盈亏</th>
                                <th class="table-cell">状态</th>
                            </tr>
                        </thead>
                        <tbody id="tradeTable">
                            <!-- 通过JavaScript动态生成交易记录 -->
                        </tbody>
                    </table>
                </div>
                <!-- 分页控制 -->
                <div class="pagination-controls">
                    <button class="page-btn" onclick="changePage('prev')" id="prevBtn">‹</button>
                    <div class="page-numbers" id="pageNumbers">
                        <button class="page-btn active" onclick="goToPage(1)">1</button>
                        <button class="page-btn" onclick="goToPage(2)">2</button>
                        <button class="page-btn" onclick="goToPage(3)">3</button>
                        <button class="page-btn" onclick="goToPage(4)">4</button>
                        <button class="page-btn" onclick="goToPage(5)">5</button>
                    </div>
                    <button class="page-btn" onclick="changePage('next')" id="nextBtn">›</button>
                </div>
            </div>
        </div>

        <!-- 右侧余额和设置区域 -->
        <div class="sidebar">
            <!-- 统一账户余额卡片 - 带切换按钮 -->
            <div class="balance-card unified-balance">
                <div class="balance-header">
                    <div class="account-switch-tabs">
                        <button class="account-tab active" id="spotAccountTab" onclick="switchAccountType('spot')">
                            🔵 现货
                        </button>
                        <button class="account-tab" id="marginAccountTab" onclick="switchAccountType('margin')">
                            🔴 杠杆
                        </button>
                    </div>
                    <div class="balance-amount" id="currentTotalBalance">$0.00</div>
                    <div class="balance-change" id="currentBalanceChange">24h变化 --</div>
                </div>

                <!-- 现货账户详情 -->
                <div class="balance-details" id="spotBalanceDetails">
                    <div class="balance-item">
                        <span class="balance-item-label">可用余额 (USDT)</span>
                        <span class="balance-item-value" id="spotAvailableBalance">$0.00</span>
                    </div>
                    <div class="balance-item">
                        <span class="balance-item-label">冻结资金</span>
                        <span class="balance-item-value frozen" id="spotFrozenBalance">$0.00</span>
                    </div>
                    <div class="balance-item">
                        <span class="balance-item-label">今日盈亏</span>
                        <span class="balance-item-value profit" id="spotDailyPnL">$0.00</span>
                    </div>
                </div>

                <!-- 杠杆账户详情 -->
                <div class="balance-details" id="marginBalanceDetails" style="display: none;">
                    <div class="balance-item">
                        <span class="balance-item-label">净资产 (USDT)</span>
                        <span class="balance-item-value" id="marginNetAsset">$0.00</span>
                    </div>
                    <div class="balance-item">
                        <span class="balance-item-label">已借资产</span>
                        <span class="balance-item-value borrowed" id="marginBorrowed">$0.00</span>
                    </div>
                    <div class="balance-item">
                        <span class="balance-item-label">风险等级</span>
                        <span class="balance-item-value risk-level" id="marginRiskLevel">安全</span>
                    </div>
                    <div class="balance-item">
                        <span class="balance-item-label">今日盈亏</span>
                        <span class="balance-item-value profit" id="marginDailyPnL">$0.00</span>
                    </div>
                </div>
            </div>

            <!-- K线图表区域 - 新增 -->
            <div class="kline-panel">
                <div class="kline-header">
                    <h3 class="panel-title" style="writing-mode: horizontal-tb; display: inline-block;">📈 实时K线图</h3>
                    <div class="kline-controls">
                        <!-- 交易对选择 -->
                        <select id="klineSymbolSelect" class="kline-select">
                            <option value="BTCUSDT">BTC/USDT</option>
                            <option value="ETHUSDT">ETH/USDT</option>
                            <option value="BNBUSDT">BNB/USDT</option>
                            <option value="ADAUSDT">ADA/USDT</option>
                            <option value="SOLUSDT">SOL/USDT</option>
                        </select>

                        <!-- 时间框架选择 -->
                        <select id="klineTimeframeSelect" class="kline-select">
                            <option value="1m">1分钟</option>
                            <option value="5m">5分钟</option>
                            <option value="15m">15分钟</option>
                            <option value="1h" selected>1小时</option>
                            <option value="4h">4小时</option>
                            <option value="1d">1天</option>
                        </select>

                        <!-- 功能按钮 -->
                        <button id="klineRefreshBtn" class="kline-btn" title="刷新数据">🔄</button>
                        <button id="klineFullscreenBtn" class="kline-btn" title="全屏显示">⛶</button>
                    </div>
                </div>

                <!-- K线图容器 -->
                <div class="kline-chart-container" id="klineChartContainer">
                    <div class="kline-loading" id="klineLoading">
                        <div class="loading-spinner"></div>
                        <div class="loading-text">加载K线数据中...</div>
                    </div>
                </div>

                <!-- 技术指标信息 -->
                <div class="kline-indicators">
                    <div class="indicator-item">
                        <span class="indicator-label">支撑位:</span>
                        <span class="indicator-value support" id="supportLevels">检测中...</span>
                    </div>
                    <div class="indicator-item">
                        <span class="indicator-label">阻力位:</span>
                        <span class="indicator-value resistance" id="resistanceLevels">检测中...</span>
                    </div>
                    <div class="indicator-item">
                        <span class="indicator-label">建议:</span>
                        <span class="indicator-value suggestion" id="tradingSuggestion">分析中...</span>
                    </div>
                </div>
            </div>

            <!-- 持仓分布图表 - 显示资产配置 -->
            <div class="position-panel">
                <h3 class="panel-title">持仓分布</h3>
                <div class="position-chart" id="positionChart">
                    <!-- 通过JavaScript动态生成持仓分布 -->
                </div>
            </div>


        </div>


    </div>

    <!-- 版权信息 -->
    <div class="copyright-info">
        <div class="copyright-text">
            断金$阿姆$库里¥ AI联盟出品<br>
            感谢chatgpt Augment cursor v0 技术支持<br>
            当前版本号：v1.0
        </div>
    </div>

    <!-- 币安API登录模态框 -->
    <div id="binanceLoginModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>币安API配置</h3>
                <span class="close" onclick="closeBinanceModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="binanceLoginForm">
                    <div class="form-group">
                        <label for="binanceApiKey">API Key:</label>
                        <input type="text" id="binanceApiKey" placeholder="请输入币安API Key" required>
                    </div>
                    <div class="form-group">
                        <label for="binanceApiSecret">API Secret:</label>
                        <input type="text" id="binanceApiSecret" placeholder="请输入币安API Secret" required>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="binanceTestnet"> 使用测试网络
                        </label>
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="testNetworkConnectivity()" class="btn-test" style="background-color: #3b82f6;">网络诊断</button>
                        <button type="button" onclick="closeBinanceModal()" class="btn-cancel">取消</button>
                        <button type="submit" class="btn-confirm">连接</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- TG机器人登录模态框 -->
    <div id="telegramLoginModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h3>Telegram机器人配置</h3>
                <span class="close" onclick="closeTelegramModal()">&times;</span>
            </div>
            <div class="modal-body">
                <form id="telegramLoginForm">
                    <div class="form-group">
                        <label for="telegramBotToken">Bot Token:</label>
                        <input type="text" id="telegramBotToken" placeholder="请输入Telegram Bot Token" required>
                    </div>
                    <div class="form-group">
                        <label for="telegramChatId">Chat ID:</label>
                        <input type="text" id="telegramChatId" placeholder="请输入Chat ID" required>
                    </div>
                    <div class="form-actions">
                        <button type="button" onclick="closeTelegramModal()" class="btn-cancel">取消</button>
                        <button type="submit" class="btn-confirm">连接</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- JavaScript模块加载 - 按依赖顺序加载 -->
    <script src="js/data.js"></script>           <!-- 数据管理模块 -->
    <script src="js/binance-api.js"></script>    <!-- 币安API集成模块 -->
    <script src="js/telegram-bot.js"></script>   <!-- Telegram机器人集成模块 -->
    <script src="js/kline-advanced.js"></script> <!-- 高级K线图模块 -->
    <script src="js/trading.js"></script>        <!-- 交易功能模块 -->
    <script src="js/ui.js"></script>             <!-- UI更新模块 -->
    <script src="js/matrix.js"></script>         <!-- 矩阵背景动画 -->

    <script src="js/main.js"></script>           <!-- 主程序入口 -->

    <!-- 现货余额修复验证脚本 -->
    <script src="balance-test.js"></script>

    <!-- 调试脚本 - 验证所有函数是否正确加载 -->
    <script>
        // 页面加载完成后进行调试检查
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🔍 调试检查开始...');

            // 检查关键函数是否存在
            const functions = [
                'updateBalanceDisplay',
                'fetchSpotAccountInfo',
                'fetchMarginAccountInfo',
                'initializeBinanceAPI',
                'connectBinanceAPI'
            ];

            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    console.log(`✅ ${funcName} 函数已加载`);
                } else {
                    console.error(`❌ ${funcName} 函数未找到`);
                }
            });

            // 检查关键DOM元素是否存在
            const elements = [
                'spotTotalBalance',
                'marginTotalBalance',
                'binanceLoginBtn'
            ];

            elements.forEach(elementId => {
                const element = document.getElementById(elementId);
                if (element) {
                    console.log(`✅ DOM元素 ${elementId} 已找到`);
                } else {
                    console.error(`❌ DOM元素 ${elementId} 未找到`);
                }
            });

            console.log('🔍 调试检查完成');
        });
    </script>
</body>
</html>
