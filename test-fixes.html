<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>修复验证页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #222;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .test-result {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
        }
        
        .success {
            background: rgba(0, 212, 170, 0.2);
            border: 1px solid #00d4aa;
        }
        
        .error {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid #ff6b6b;
        }
        
        .btn {
            background: #00d4aa;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        
        .btn:hover {
            background: #00b894;
        }
        
        .kline-preview {
            width: 100%;
            height: 300px;
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🔧 修复验证页面</h1>
        
        <!-- TG机器人测试 -->
        <div class="test-section">
            <h2>🤖 TG机器人持久化测试</h2>
            <p>测试TG机器人登录状态是否在页面刷新后保持</p>
            
            <button class="btn" onclick="testTGStatus()">检查TG状态</button>
            <button class="btn" onclick="simulateTGLogin()">模拟TG登录</button>
            <button class="btn" onclick="location.reload()">刷新页面测试</button>
            
            <div id="tgTestResult" class="test-result">等待测试...</div>
        </div>
        
        <!-- K线图测试 -->
        <div class="test-section">
            <h2>📈 实盘K线图测试</h2>
            <p>测试是否能正确显示币安实盘K线数据</p>
            
            <button class="btn" onclick="testKlineAPI()">测试API</button>
            <button class="btn" onclick="loadKlineChart()">加载K线图</button>
            
            <div id="klineTestResult" class="test-result">等待测试...</div>
            <div id="klinePreview" class="kline-preview">
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                    点击"加载K线图"开始
                </div>
            </div>
        </div>
        
        <!-- 余额测试 -->
        <div class="test-section">
            <h2>💰 账户余额测试</h2>
            <p>测试账户余额是否在页面刷新后正确显示</p>
            
            <button class="btn" onclick="testBalanceAPI()">测试余额API</button>
            <button class="btn" onclick="simulateBalanceData()">模拟余额数据</button>
            
            <div id="balanceTestResult" class="test-result">等待测试...</div>
        </div>
    </div>

    <script>
        // 更新测试结果
        function updateTestResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            element.className = `test-result ${isSuccess ? 'success' : 'error'}`;
            console.log(message);
        }
        
        // 测试TG状态
        function testTGStatus() {
            const savedStatus = localStorage.getItem('telegramStatus');
            if (savedStatus) {
                try {
                    const parsed = JSON.parse(savedStatus);
                    updateTestResult('tgTestResult', 
                        `✅ TG状态已保存: connected=${parsed.connected}, time=${parsed.connectionTime}`, true);
                } catch (error) {
                    updateTestResult('tgTestResult', `❌ TG状态解析失败: ${error.message}`, false);
                }
            } else {
                updateTestResult('tgTestResult', '⚠️ 未找到保存的TG状态', false);
            }
        }
        
        // 模拟TG登录
        function simulateTGLogin() {
            const status = {
                connected: true,
                connectionTime: new Date().toISOString()
            };
            localStorage.setItem('telegramStatus', JSON.stringify(status));
            updateTestResult('tgTestResult', '✅ TG登录状态已保存，请刷新页面测试持久化', true);
        }
        
        // 测试K线API
        async function testKlineAPI() {
            try {
                updateTestResult('klineTestResult', '🔄 测试币安K线API...', true);
                
                const response = await fetch('/api/binance/klines?symbol=BTCUSDT&interval=1h&limit=5');
                const data = await response.json();
                
                if (data.success) {
                    const currentPrice = parseFloat(data.data[data.data.length - 1][4]);
                    updateTestResult('klineTestResult', 
                        `✅ API测试成功，获取${data.data.length}条数据，BTC当前价格: $${currentPrice.toFixed(2)}`, true);
                } else {
                    updateTestResult('klineTestResult', `❌ API测试失败: ${data.error}`, false);
                }
            } catch (error) {
                updateTestResult('klineTestResult', `❌ API测试异常: ${error.message}`, false);
            }
        }
        
        // 加载K线图
        async function loadKlineChart() {
            try {
                updateTestResult('klineTestResult', '🎨 加载实盘K线图...', true);
                
                const response = await fetch('/api/binance/klines?symbol=BTCUSDT&interval=1h&limit=30');
                const data = await response.json();
                
                if (!data.success) {
                    throw new Error(data.error);
                }
                
                // 绘制简单K线图
                drawSimpleKline(data.data);
                updateTestResult('klineTestResult', 
                    `✅ K线图加载成功，显示${data.data.length}根K线`, true);
                
            } catch (error) {
                updateTestResult('klineTestResult', `❌ K线图加载失败: ${error.message}`, false);
            }
        }
        
        // 绘制简单K线图
        function drawSimpleKline(klineData) {
            const container = document.getElementById('klinePreview');
            container.innerHTML = '';
            
            const canvas = document.createElement('canvas');
            canvas.width = container.clientWidth;
            canvas.height = container.clientHeight;
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            container.appendChild(canvas);
            
            const ctx = canvas.getContext('2d');
            
            // 转换数据
            const candleData = klineData.map(kline => ({
                open: parseFloat(kline[1]),
                high: parseFloat(kline[2]),
                low: parseFloat(kline[3]),
                close: parseFloat(kline[4])
            }));
            
            // 计算价格范围
            const prices = candleData.flatMap(d => [d.high, d.low]);
            const minPrice = Math.min(...prices);
            const maxPrice = Math.max(...prices);
            const priceRange = maxPrice - minPrice;
            
            const width = canvas.width;
            const height = canvas.height;
            const padding = 40;
            const chartWidth = width - padding * 2;
            const chartHeight = height - padding * 2;
            
            // 清空画布
            ctx.fillStyle = '#1a1a1a';
            ctx.fillRect(0, 0, width, height);
            
            // 绘制K线
            const candleWidth = chartWidth / candleData.length * 0.8;
            const candleSpacing = chartWidth / candleData.length;
            
            candleData.forEach((candle, index) => {
                const x = padding + index * candleSpacing + candleSpacing / 2;
                
                const openY = padding + (maxPrice - candle.open) / priceRange * chartHeight;
                const highY = padding + (maxPrice - candle.high) / priceRange * chartHeight;
                const lowY = padding + (maxPrice - candle.low) / priceRange * chartHeight;
                const closeY = padding + (maxPrice - candle.close) / priceRange * chartHeight;
                
                const isRising = candle.close > candle.open;
                const color = isRising ? '#00d4aa' : '#ff6b6b';
                
                // 绘制影线
                ctx.strokeStyle = color;
                ctx.lineWidth = 1;
                ctx.beginPath();
                ctx.moveTo(x, highY);
                ctx.lineTo(x, lowY);
                ctx.stroke();
                
                // 绘制实体
                ctx.fillStyle = color;
                const bodyTop = Math.min(openY, closeY);
                const bodyHeight = Math.abs(closeY - openY);
                ctx.fillRect(x - candleWidth / 2, bodyTop, candleWidth, Math.max(bodyHeight, 1));
            });
            
            // 绘制标题
            ctx.fillStyle = '#00d4aa';
            ctx.font = 'bold 14px Arial';
            ctx.textAlign = 'left';
            ctx.fillText('📈 BTC/USDT 实盘K线', padding, 25);
            
            // 绘制当前价格
            const currentPrice = candleData[candleData.length - 1].close;
            ctx.fillStyle = '#ffd700';
            ctx.font = 'bold 12px Arial';
            ctx.textAlign = 'right';
            ctx.fillText(`$${currentPrice.toFixed(2)}`, width - padding, 25);
        }
        
        // 测试余额API
        async function testBalanceAPI() {
            try {
                updateTestResult('balanceTestResult', '🔄 测试余额API...', true);
                
                // 测试现货账户
                const spotResponse = await fetch('/api/binance/account');
                const spotData = await spotResponse.json();
                
                if (spotData.success) {
                    updateTestResult('balanceTestResult', '✅ 现货账户API正常', true);
                } else {
                    updateTestResult('balanceTestResult', `⚠️ 现货账户API: ${spotData.error}`, false);
                }
                
            } catch (error) {
                updateTestResult('balanceTestResult', `❌ 余额API测试失败: ${error.message}`, false);
            }
        }
        
        // 模拟余额数据
        function simulateBalanceData() {
            // 模拟设置余额数据
            window.spotAccountBalance = {
                totalBalance: 1000.50,
                availableBalance: 950.25,
                frozenBalance: 50.25,
                dailyPnL: 25.75
            };
            
            window.marginAccountBalance = {
                totalBalance: 2000.75,
                netAsset: 1800.50,
                borrowed: 200.25,
                dailyPnL: -15.25
            };
            
            updateTestResult('balanceTestResult', '✅ 模拟余额数据已设置', true);
        }
        
        // 页面加载完成后自动测试
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 修复验证页面加载完成');
            
            // 自动检查TG状态
            setTimeout(testTGStatus, 1000);
            
            // 自动测试K线API
            setTimeout(testKlineAPI, 2000);
        });
    </script>
</body>
</html>
