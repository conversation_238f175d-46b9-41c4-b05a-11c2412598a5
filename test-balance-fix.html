<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>现货余额修复测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #222;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .btn {
            background: #00d4aa;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        
        .btn:hover {
            background: #00b894;
        }
        
        .result {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .success {
            background: rgba(0, 212, 170, 0.2);
            border: 1px solid #00d4aa;
        }
        
        .error {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid #ff6b6b;
        }
        
        .balance-display {
            background: #333;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .balance-item {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>💰 现货余额修复测试</h1>
        
        <div class="test-section">
            <h2>问题描述</h2>
            <p>修复前：现货余额显示$0.00，需要点击杠杆再点击现货才显示正确金额</p>
            <p>修复后：页面加载时应该直接显示正确的现货余额</p>
        </div>
        
        <div class="test-section">
            <h2>数据状态检查</h2>
            <button class="btn" onclick="checkDataStatus()">检查数据状态</button>
            <button class="btn" onclick="simulateAccountData()">模拟账户数据</button>
            <button class="btn" onclick="testAccountSwitch()">测试账户切换</button>
            
            <div id="dataResult" class="result">等待检查...</div>
        </div>
        
        <div class="test-section">
            <h2>余额显示测试</h2>
            <div class="balance-display">
                <h3>现货账户</h3>
                <div class="balance-item">
                    <span>总余额:</span>
                    <span id="testSpotTotal">$0.00</span>
                </div>
                <div class="balance-item">
                    <span>可用余额:</span>
                    <span id="testSpotAvailable">$0.00</span>
                </div>
                <div class="balance-item">
                    <span>冻结余额:</span>
                    <span id="testSpotFrozen">$0.00</span>
                </div>
            </div>
            
            <div class="balance-display">
                <h3>杠杆账户</h3>
                <div class="balance-item">
                    <span>净资产:</span>
                    <span id="testMarginNet">$0.00</span>
                </div>
                <div class="balance-item">
                    <span>总资产:</span>
                    <span id="testMarginTotal">$0.00</span>
                </div>
                <div class="balance-item">
                    <span>借贷:</span>
                    <span id="testMarginBorrowed">$0.00</span>
                </div>
            </div>
            
            <button class="btn" onclick="updateTestDisplay()">更新显示</button>
            <button class="btn" onclick="location.href='/'">返回主页测试</button>
        </div>
    </div>

    <script>
        // 更新结果显示
        function updateResult(message, isSuccess = true) {
            const element = document.getElementById('dataResult');
            element.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            console.log(message);
        }
        
        // 检查数据状态
        function checkDataStatus() {
            updateResult('🔍 检查账户数据状态...', true);
            
            const checks = [];
            
            // 检查API状态
            if (window.apiStatus) {
                checks.push(`✅ API状态: connected=${window.apiStatus.connected}`);
            } else {
                checks.push('❌ API状态: 未定义');
            }
            
            // 检查现货账户数据
            if (window.spotAccountBalance) {
                checks.push(`✅ 现货账户数据: 总余额=$${window.spotAccountBalance.totalBalance?.toFixed(2) || '0.00'}`);
                checks.push(`   可用余额=$${window.spotAccountBalance.availableBalance?.toFixed(2) || '0.00'}`);
            } else {
                checks.push('❌ 现货账户数据: 未定义');
            }
            
            // 检查杠杆账户数据
            if (window.marginAccountBalance) {
                checks.push(`✅ 杠杆账户数据: 净资产=$${window.marginAccountBalance.netAsset?.toFixed(2) || '0.00'}`);
            } else {
                checks.push('❌ 杠杆账户数据: 未定义');
            }
            
            // 检查关键函数
            const functions = ['switchAccountType', 'updateBalanceDisplay', 'fetchSpotAccountInfo'];
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    checks.push(`✅ 函数 ${funcName}: 已加载`);
                } else {
                    checks.push(`❌ 函数 ${funcName}: 未加载`);
                }
            });
            
            updateResult(checks.join('\n'), true);
        }
        
        // 模拟账户数据
        function simulateAccountData() {
            updateResult('🎭 设置模拟账户数据...', true);
            
            // 模拟现货账户数据
            window.spotAccountBalance = {
                totalBalance: 1234.56,
                availableBalance: 1000.00,
                frozenBalance: 234.56,
                dailyPnL: 45.67,
                dailyChangePercent: 3.2
            };
            
            // 模拟杠杆账户数据
            window.marginAccountBalance = {
                netAsset: 2345.67,
                totalAsset: 2500.00,
                totalLiability: 154.33,
                dailyPnL: -12.34,
                dailyChangePercent: -0.5
            };
            
            // 模拟API连接状态
            if (!window.apiStatus) {
                window.apiStatus = {};
            }
            window.apiStatus.connected = true;
            
            updateResult('✅ 模拟数据设置完成', true);
            updateTestDisplay();
        }
        
        // 测试账户切换
        function testAccountSwitch() {
            updateResult('🔄 测试账户切换功能...', true);
            
            if (typeof window.switchAccountType === 'function') {
                // 测试切换到现货
                window.switchAccountType('spot');
                setTimeout(() => {
                    // 测试切换到杠杆
                    window.switchAccountType('margin');
                    setTimeout(() => {
                        // 切换回现货
                        window.switchAccountType('spot');
                        updateResult('✅ 账户切换测试完成', true);
                    }, 1000);
                }, 1000);
            } else {
                updateResult('❌ switchAccountType函数未找到', false);
            }
        }
        
        // 更新测试显示
        function updateTestDisplay() {
            // 更新现货账户显示
            if (window.spotAccountBalance) {
                document.getElementById('testSpotTotal').textContent = 
                    `$${window.spotAccountBalance.totalBalance.toFixed(2)}`;
                document.getElementById('testSpotAvailable').textContent = 
                    `$${window.spotAccountBalance.availableBalance.toFixed(2)}`;
                document.getElementById('testSpotFrozen').textContent = 
                    `$${window.spotAccountBalance.frozenBalance.toFixed(2)}`;
            }
            
            // 更新杠杆账户显示
            if (window.marginAccountBalance) {
                document.getElementById('testMarginNet').textContent = 
                    `$${window.marginAccountBalance.netAsset.toFixed(2)}`;
                document.getElementById('testMarginTotal').textContent = 
                    `$${window.marginAccountBalance.totalAsset.toFixed(2)}`;
                document.getElementById('testMarginBorrowed').textContent = 
                    `$${window.marginAccountBalance.totalLiability.toFixed(2)}`;
            }
            
            updateResult('✅ 测试显示已更新', true);
        }
        
        // 页面加载完成后自动检查
        document.addEventListener('DOMContentLoaded', function() {
            console.log('📄 现货余额修复测试页面加载完成');
            setTimeout(checkDataStatus, 1000);
        });
    </script>
</body>
</html>
