<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>紧急修复 - 断金交易系统</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .status-section {
            background: #222;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .btn {
            background: #00d4aa;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        
        .btn:hover {
            background: #00b894;
        }
        
        .btn.error {
            background: #ff6b6b;
            color: #fff;
        }
        
        .result {
            padding: 10px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
        }
        
        .success {
            background: rgba(0, 212, 170, 0.2);
            border: 1px solid #00d4aa;
        }
        
        .error {
            background: rgba(255, 107, 107, 0.2);
            border: 1px solid #ff6b6b;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚨 紧急修复页面</h1>
        
        <div class="status-section">
            <h2>系统状态检查</h2>
            <button class="btn" onclick="checkSystemStatus()">检查系统状态</button>
            <button class="btn" onclick="testBasicFunctions()">测试基本功能</button>
            <button class="btn" onclick="clearAllCache()">清除缓存</button>
            <button class="btn" onclick="location.href='/'">返回主页</button>
            
            <div id="statusResult" class="result">等待检查...</div>
        </div>
        
        <div class="status-section">
            <h2>API连接测试</h2>
            <button class="btn" onclick="testBinanceAPI()">测试币安API</button>
            <button class="btn" onclick="testTelegramAPI()">测试TG API</button>
            
            <div id="apiResult" class="result">等待测试...</div>
        </div>
        
        <div class="status-section">
            <h2>JavaScript错误检查</h2>
            <button class="btn" onclick="checkJSErrors()">检查JS错误</button>
            <button class="btn" onclick="reloadScripts()">重新加载脚本</button>
            
            <div id="jsResult" class="result">等待检查...</div>
        </div>
    </div>

    <script>
        // 捕获所有JavaScript错误
        window.onerror = function(msg, url, line, col, error) {
            console.error('JavaScript错误:', {msg, url, line, col, error});
            updateResult('jsResult', `❌ JS错误: ${msg} (行${line})`, false);
            return false;
        };
        
        // 更新结果显示
        function updateResult(elementId, message, isSuccess = true) {
            const element = document.getElementById(elementId);
            element.textContent = `[${new Date().toLocaleTimeString()}] ${message}`;
            element.className = `result ${isSuccess ? 'success' : 'error'}`;
            console.log(message);
        }
        
        // 检查系统状态
        function checkSystemStatus() {
            updateResult('statusResult', '🔍 检查系统状态...', true);
            
            const checks = [];
            
            // 检查DOM元素
            const elements = [
                'binanceLoginBtn',
                'telegramLoginBtn', 
                'klineChartContainer',
                'spotTotalBalance'
            ];
            
            elements.forEach(id => {
                const element = document.getElementById(id);
                if (element) {
                    checks.push(`✅ ${id}: 存在`);
                } else {
                    checks.push(`❌ ${id}: 缺失`);
                }
            });
            
            // 检查全局变量
            const globals = ['window.apiStatus', 'window.telegramStatus'];
            globals.forEach(varName => {
                try {
                    const value = eval(varName);
                    checks.push(`✅ ${varName}: ${value ? '已定义' : '未定义'}`);
                } catch (e) {
                    checks.push(`❌ ${varName}: 错误`);
                }
            });
            
            updateResult('statusResult', checks.join('\n'), true);
        }
        
        // 测试基本功能
        function testBasicFunctions() {
            updateResult('statusResult', '🧪 测试基本功能...', true);
            
            const tests = [];
            
            // 测试localStorage
            try {
                localStorage.setItem('test', 'ok');
                localStorage.removeItem('test');
                tests.push('✅ localStorage: 正常');
            } catch (e) {
                tests.push('❌ localStorage: 异常');
            }
            
            // 测试fetch
            try {
                fetch('/api/binance/time').then(() => {
                    tests.push('✅ fetch API: 正常');
                }).catch(() => {
                    tests.push('❌ fetch API: 异常');
                });
            } catch (e) {
                tests.push('❌ fetch API: 不支持');
            }
            
            updateResult('statusResult', tests.join('\n'), true);
        }
        
        // 清除所有缓存
        function clearAllCache() {
            try {
                localStorage.clear();
                sessionStorage.clear();
                updateResult('statusResult', '✅ 缓存已清除，请刷新页面', true);
            } catch (e) {
                updateResult('statusResult', '❌ 缓存清除失败', false);
            }
        }
        
        // 测试币安API
        async function testBinanceAPI() {
            try {
                updateResult('apiResult', '🔄 测试币安API...', true);
                
                const response = await fetch('/api/binance/time');
                const data = await response.json();
                
                if (data.success) {
                    updateResult('apiResult', '✅ 币安API连接正常', true);
                } else {
                    updateResult('apiResult', `❌ 币安API错误: ${data.error}`, false);
                }
            } catch (error) {
                updateResult('apiResult', `❌ 币安API测试失败: ${error.message}`, false);
            }
        }
        
        // 测试TG API
        async function testTelegramAPI() {
            try {
                updateResult('apiResult', '🔄 测试TG API...', true);
                
                const response = await fetch('/api/telegram/getMe');
                const data = await response.json();
                
                if (data.success) {
                    updateResult('apiResult', '✅ TG API连接正常', true);
                } else {
                    updateResult('apiResult', `❌ TG API错误: ${data.error}`, false);
                }
            } catch (error) {
                updateResult('apiResult', `❌ TG API测试失败: ${error.message}`, false);
            }
        }
        
        // 检查JS错误
        function checkJSErrors() {
            updateResult('jsResult', '🔍 检查JavaScript状态...', true);
            
            const checks = [];
            
            // 检查关键函数是否存在
            const functions = [
                'toggleBinanceConnection',
                'toggleTelegramConnection',
                'updateBalanceDisplay',
                'showSimpleKlineChart'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    checks.push(`✅ ${funcName}: 已加载`);
                } else {
                    checks.push(`❌ ${funcName}: 未加载`);
                }
            });
            
            updateResult('jsResult', checks.join('\n'), true);
        }
        
        // 重新加载脚本
        function reloadScripts() {
            updateResult('jsResult', '🔄 重新加载页面...', true);
            setTimeout(() => {
                location.reload();
            }, 1000);
        }
        
        // 页面加载完成后自动检查
        document.addEventListener('DOMContentLoaded', function() {
            console.log('🚨 紧急修复页面加载完成');
            setTimeout(checkSystemStatus, 1000);
        });
    </script>
</body>
</html>
