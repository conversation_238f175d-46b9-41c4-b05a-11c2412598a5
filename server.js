// 断金ใ现货'杠杆自动化交易系统 - 后端服务器
// 功能：API密钥安全存储、币安API代理、Telegram机器人代理
// 安全性：API密钥不暴露给前端，所有请求通过后端代理

const express = require('express');
const cors = require('cors');
const crypto = require('crypto');
const axios = require('axios');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件配置
app.use(cors()); // 允许跨域请求
app.use(express.json()); // 解析JSON请求体
app.use(express.static('.')); // 静态文件服务

// 币安API域名配置 - 根据官方文档更新的完整域名列表
const BINANCE_DOMAINS = {
    MAIN: [
        'https://api.binance.com',
        'https://api-gcp.binance.com',
        'https://api1.binance.com',
        'https://api2.binance.com',
        'https://api3.binance.com',
        'https://api4.binance.com'
    ],
    TESTNET: [
        'https://testnet.binance.vision'
    ]
};

// API配置 - 直接配置您的API密钥
let CONFIG = {
    BINANCE: {
        API_KEY: 'dsu9xCnyxw9Yzz43a8rGn6cIj8NpWzOQE2HIVjAOXA8gurIDj9wKaWv0JV8vFheE',
        API_SECRET: 'AR1EhHrxB5eZj8c3ByphL42ghpbmip3AvcgP1ntqqzai9uf2BJQufVq5ct4dPG19',
        BASE_URL: 'https://api.binance.com',
        BACKUP_URLS: BINANCE_DOMAINS.MAIN
    },
    TELEGRAM: {
        BOT_TOKEN: '**********************************************',
        CHAT_ID: '7818062567'
    }
};

// 生成币安API签名
function generateBinanceSignature(queryString, secret) {
    return crypto
        .createHmac('sha256', secret)
        .update(queryString)
        .digest('hex');
}

// 智能币安API请求封装 - 支持多域名自动切换，严格按照官方文档实现
async function binanceRequest(endpoint, params = {}, method = 'GET', signed = false) {
    // 验证API配置
    if (signed && (!CONFIG.BINANCE.API_KEY || !CONFIG.BINANCE.API_SECRET)) {
        throw new Error('API Key或Secret未配置');
    }

    const timestamp = Date.now();
    let queryString = '';

    // 构建查询字符串
    if (Object.keys(params).length > 0) {
        queryString = new URLSearchParams(params).toString();
    }

    if (signed) {
        // 添加时间戳和recvWindow
        const signParams = {
            ...params,
            timestamp: timestamp,
            recvWindow: 5000
        };

        // 重新构建查询字符串用于签名
        queryString = new URLSearchParams(signParams).toString();

        // 生成签名 - 严格按照币安官方文档
        const signature = generateBinanceSignature(queryString, CONFIG.BINANCE.API_SECRET);
        queryString += `&signature=${signature}`;

        console.log(`🔐 签名调试信息:`, {
            endpoint,
            queryStringForSign: new URLSearchParams(signParams).toString(),
            signature: signature.substring(0, 16) + '...',
            timestamp
        });
    }

    const headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    };

    // 对于需要API Key的请求，添加API Key头部
    if (signed || endpoint.includes('/account') || endpoint.includes('/order')) {
        headers['X-MBX-APIKEY'] = CONFIG.BINANCE.API_KEY;
    }

    // 尝试多个域名
    const urlsToTry = [CONFIG.BINANCE.BASE_URL, ...CONFIG.BINANCE.BACKUP_URLS];
    let lastError = null;

    for (let i = 0; i < urlsToTry.length; i++) {
        const baseUrl = urlsToTry[i];
        const url = `${baseUrl}${endpoint}`;

        try {
            console.log(`📡 币安API请求 (${i + 1}/${urlsToTry.length}): ${method} ${endpoint} -> ${baseUrl}`);

            const axiosConfig = {
                method,
                url,
                headers,
                timeout: 15000, // 15秒超时
                validateStatus: function (status) {
                    return status < 500; // 只有5xx错误才重试
                }
            };

            // 根据请求方法添加参数
            if (method === 'GET' && queryString) {
                axiosConfig.url += `?${queryString}`;
            } else if (method === 'POST' && queryString) {
                axiosConfig.data = queryString;
            }

            const response = await axios(axiosConfig);

            if (response.status >= 400) {
                // 4xx错误不重试，直接返回
                console.error(`❌ API错误 ${response.status}:`, response.data);
                return {
                    success: false,
                    error: response.data?.msg || `HTTP ${response.status}: ${response.data?.code || 'Unknown'}`
                };
            }

            // 成功，更新主域名
            if (i > 0) {
                CONFIG.BINANCE.BASE_URL = baseUrl;
                console.log(`✅ 切换到备用域名: ${baseUrl}`);
            }

            console.log(`✅ API请求成功: ${endpoint}`);
            return { success: true, data: response.data };

        } catch (error) {
            lastError = error;
            console.warn(`⚠️ 域名 ${baseUrl} 连接失败:`, error.response?.data || error.message);

            // 如果是网络错误或超时，尝试下一个域名
            if (error.code === 'ECONNREFUSED' ||
                error.code === 'ETIMEDOUT' ||
                error.code === 'ENOTFOUND' ||
                error.code === 'ECONNRESET' ||
                error.message.includes('timeout')) {
                continue;
            }

            // API错误直接返回
            if (error.response && error.response.data) {
                return {
                    success: false,
                    error: error.response.data.msg || error.response.data.code || error.message
                };
            }

            // 其他错误继续尝试下一个域名
            continue;
        }
    }

    // 所有域名都失败
    console.error('❌ 所有币安API域名都无法连接');
    return {
        success: false,
        error: `网络连接失败，已尝试 ${urlsToTry.length} 个域名: ${lastError?.message || '未知错误'}`
    };
}

// Telegram API请求封装
async function telegramRequest(method, params = {}) {
    try {
        const url = `https://api.telegram.org/bot${CONFIG.TELEGRAM.BOT_TOKEN}/${method}`;
        
        console.log(`📤 Telegram API请求: ${method}`);
        
        const response = await axios.post(url, {
            chat_id: CONFIG.TELEGRAM.CHAT_ID,
            ...params
        });
        
        return { success: true, result: response.data.result };
        
    } catch (error) {
        console.error('❌ Telegram API请求失败:', error.response?.data || error.message);
        return { 
            success: false, 
            error: error.response?.data?.description || error.message 
        };
    }
}

// 路由定义

// 首页路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, 'index.html'));
});

// 币安API路由

// 设置币安API配置
app.post('/api/binance/config', (req, res) => {
    try {
        const { apiKey, apiSecret, useTestnet } = req.body;

        if (!apiKey || !apiSecret) {
            return res.json({
                success: false,
                error: 'API Key和Secret不能为空'
            });
        }

        // 更新配置
        CONFIG.BINANCE.API_KEY = apiKey;
        CONFIG.BINANCE.API_SECRET = apiSecret;

        if (useTestnet) {
            CONFIG.BINANCE.BASE_URL = BINANCE_DOMAINS.TESTNET[0];
            CONFIG.BINANCE.BACKUP_URLS = BINANCE_DOMAINS.TESTNET;
        } else {
            CONFIG.BINANCE.BASE_URL = BINANCE_DOMAINS.MAIN[0];
            CONFIG.BINANCE.BACKUP_URLS = BINANCE_DOMAINS.MAIN;
        }

        console.log('🔑 币安API配置已更新:', {
            apiKey: apiKey.substring(0, 8) + '...',
            secretLength: apiSecret.length,
            useTestnet,
            baseUrl: CONFIG.BINANCE.BASE_URL,
            backupUrls: CONFIG.BINANCE.BACKUP_URLS.length
        });

        res.json({
            success: true,
            message: '币安API配置已更新'
        });

    } catch (error) {
        console.error('❌ 设置币安API配置失败:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

// 验证API Key格式
function validateApiKeyFormat(apiKey, apiSecret) {
    // 币安API Key通常是64个字符的字母数字字符串
    if (!apiKey || apiKey.length < 60 || apiKey.length > 70) {
        return { valid: false, error: 'API Key长度不正确，应该是60-70个字符' };
    }

    // 币安API Secret通常是64个字符的字母数字字符串
    if (!apiSecret || apiSecret.length < 60 || apiSecret.length > 70) {
        return { valid: false, error: 'API Secret长度不正确，应该是60-70个字符' };
    }

    // 检查是否包含有效字符
    const validChars = /^[A-Za-z0-9]+$/;
    if (!validChars.test(apiKey)) {
        return { valid: false, error: 'API Key包含无效字符，只能包含字母和数字' };
    }

    if (!validChars.test(apiSecret)) {
        return { valid: false, error: 'API Secret包含无效字符，只能包含字母和数字' };
    }

    return { valid: true };
}

// 测试API连接
app.post('/api/binance/test', async (req, res) => {
    try {
        const { apiKey, apiSecret, useTestnet } = req.body;

        if (!apiKey || !apiSecret) {
            return res.json({
                success: false,
                error: 'API Key和Secret不能为空'
            });
        }

        // 验证API Key格式
        const validation = validateApiKeyFormat(apiKey, apiSecret);
        if (!validation.valid) {
            return res.json({
                success: false,
                error: validation.error
            });
        }

        // 临时设置配置进行测试
        const originalConfig = { ...CONFIG.BINANCE };
        CONFIG.BINANCE.API_KEY = apiKey.trim();
        CONFIG.BINANCE.API_SECRET = apiSecret.trim();

        if (useTestnet) {
            CONFIG.BINANCE.BASE_URL = BINANCE_DOMAINS.TESTNET[0];
            CONFIG.BINANCE.BACKUP_URLS = BINANCE_DOMAINS.TESTNET;
        } else {
            CONFIG.BINANCE.BASE_URL = BINANCE_DOMAINS.MAIN[0];
            CONFIG.BINANCE.BACKUP_URLS = BINANCE_DOMAINS.MAIN;
        }

        console.log('🧪 测试币安API连接:', {
            apiKey: apiKey.substring(0, 8) + '...',
            secretLength: apiSecret.length,
            useTestnet,
            baseUrl: CONFIG.BINANCE.BASE_URL
        });

        // 测试服务器时间（不需要签名）
        console.log('📡 测试服务器时间...');
        const timeResult = await binanceRequest('/api/v3/time');
        if (!timeResult.success) {
            CONFIG.BINANCE = originalConfig; // 恢复原配置
            return res.json({
                success: false,
                error: '无法连接到币安服务器: ' + timeResult.error
            });
        }

        console.log('✅ 服务器时间获取成功，测试账户信息...');

        // 测试账户信息（需要签名）
        const accountResult = await binanceRequest('/api/v3/account', {}, 'GET', true);

        // 恢复原配置
        CONFIG.BINANCE = originalConfig;

        if (accountResult.success) {
            console.log('✅ API连接测试完全成功');
            res.json({
                success: true,
                message: 'API连接测试成功',
                data: {
                    serverTime: timeResult.data.serverTime,
                    accountType: accountResult.data.accountType || 'SPOT',
                    balanceCount: accountResult.data.balances ? accountResult.data.balances.length : 0
                }
            });
        } else {
            console.error('❌ 账户信息获取失败:', accountResult.error);
            res.json({
                success: false,
                error: 'API认证失败: ' + accountResult.error
            });
        }

    } catch (error) {
        console.error('❌ API测试失败:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

// 网络连接测试
app.get('/api/binance/connectivity', async (req, res) => {
    try {
        const results = [];

        // 测试所有主网域名
        for (const url of BINANCE_DOMAINS.MAIN) {
            try {
                const start = Date.now();
                const response = await axios.get(`${url}/api/v3/time`, { timeout: 5000 });
                const latency = Date.now() - start;

                results.push({
                    url,
                    status: 'success',
                    latency: `${latency}ms`,
                    serverTime: response.data.serverTime
                });
            } catch (error) {
                results.push({
                    url,
                    status: 'failed',
                    error: error.message
                });
            }
        }

        res.json({
            success: true,
            results,
            recommendation: results.find(r => r.status === 'success')?.url || BINANCE_DOMAINS.MAIN[0]
        });

    } catch (error) {
        res.json({
            success: false,
            error: error.message
        });
    }
});

// 获取服务器时间
app.get('/api/binance/time', async (req, res) => {
    const result = await binanceRequest('/api/v3/time');
    res.json(result);
});

// 获取24小时价格变动
app.get('/api/binance/ticker/24hr', async (req, res) => {
    const result = await binanceRequest('/api/v3/ticker/24hr');
    res.json(result);
});

// 获取K线数据 - 新增
app.get('/api/binance/klines', async (req, res) => {
    try {
        const { symbol, interval = '1h', limit = 100, startTime, endTime } = req.query;

        console.log(`📊 收到K线数据请求: ${symbol} ${interval} limit=${limit}`);

        if (!symbol) {
            return res.json({
                success: false,
                error: '缺少必需的symbol参数'
            });
        }

        // 构建请求参数
        const params = {
            symbol: symbol.toUpperCase(),
            interval: interval,
            limit: Math.min(parseInt(limit), 1000) // 限制最大1000根K线
        };

        // 可选的时间范围参数
        if (startTime) params.startTime = parseInt(startTime);
        if (endTime) params.endTime = parseInt(endTime);

        console.log('📈 K线请求参数:', params);

        // 调用币安API
        const result = await binanceRequest('/api/v3/klines', params);

        if (result.success) {
            console.log(`✅ K线数据获取成功: ${result.data.length} 根K线`);
        } else {
            console.error('❌ K线数据获取失败:', result.error);
        }

        res.json(result);

    } catch (error) {
        console.error('❌ K线数据API错误:', error);
        res.json({
            success: false,
            error: error.message || 'K线数据获取失败'
        });
    }
});

// 获取现货账户信息（需要签名）
app.get('/api/binance/account', async (req, res) => {
    console.log('📊 收到现货账户信息请求');

    if (!CONFIG.BINANCE.API_KEY || !CONFIG.BINANCE.API_SECRET) {
        console.error('❌ API Key未配置');
        return res.json({
            success: false,
            error: 'API Key未配置，请先设置币安API配置'
        });
    }

    console.log('🔑 API配置检查通过，开始请求账户信息');
    console.log('🔍 API Key前缀:', CONFIG.BINANCE.API_KEY.substring(0, 8) + '...');

    try {
        const result = await binanceRequest('/api/v3/account', {}, 'GET', true);
        console.log('📊 现货账户信息请求结果:', result.success ? '成功' : '失败');
        if (!result.success) {
            console.error('❌ 现货账户信息错误:', result.error);
        }
        res.json(result);
    } catch (error) {
        console.error('❌ 现货账户信息请求异常:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

// 获取杠杆全仓账户信息（需要签名）
app.get('/api/binance/margin/account', async (req, res) => {
    console.log('📊 收到杠杆账户信息请求');

    if (!CONFIG.BINANCE.API_KEY || !CONFIG.BINANCE.API_SECRET) {
        console.error('❌ API Key未配置');
        return res.json({
            success: false,
            error: 'API Key未配置，请先设置币安API配置'
        });
    }

    console.log('🔑 API配置检查通过，开始请求杠杆账户信息');

    try {
        const result = await binanceRequest('/sapi/v1/margin/account', {}, 'GET', true);
        console.log('📊 杠杆账户信息请求结果:', result.success ? '成功' : '失败');
        if (!result.success) {
            console.error('❌ 杠杆账户信息错误:', result.error);
        }
        res.json(result);
    } catch (error) {
        console.error('❌ 杠杆账户信息请求异常:', error);
        res.json({
            success: false,
            error: error.message
        });
    }
});

// 获取交易历史（需要签名）
app.get('/api/binance/myTrades', async (req, res) => {
    const { symbol, limit = 10 } = req.query;

    // 检查必需的symbol参数
    if (!symbol) {
        return res.json({
            success: false,
            error: 'Symbol parameter is required'
        });
    }

    const result = await binanceRequest('/api/v3/myTrades', { symbol, limit }, 'GET', true);
    res.json(result);
});

// 下单交易（需要签名）
app.post('/api/binance/order', async (req, res) => {
    const { symbol, side, type, quantity, price, timeInForce } = req.body;
    
    const params = {
        symbol,
        side,
        type,
        quantity
    };
    
    // 限价单需要价格
    if (type === 'LIMIT') {
        params.price = price;
        params.timeInForce = timeInForce || 'GTC';
    }
    
    const result = await binanceRequest('/api/v3/order', params, 'POST', true);
    res.json(result);
});

// 取消订单（需要签名）
app.delete('/api/binance/order', async (req, res) => {
    const { symbol, orderId } = req.body;
    const result = await binanceRequest('/api/v3/order', { symbol, orderId }, 'DELETE', true);
    res.json(result);
});

// 获取当前挂单（需要签名）
app.get('/api/binance/openOrders', async (req, res) => {
    const { symbol } = req.query;
    const params = symbol ? { symbol } : {};
    const result = await binanceRequest('/api/v3/openOrders', params, 'GET', true);
    res.json(result);
});

// Telegram机器人路由

// 获取机器人信息
app.get('/api/telegram/getMe', async (req, res) => {
    const result = await telegramRequest('getMe');
    res.json(result);
});

// 发送消息
app.post('/api/telegram/sendMessage', async (req, res) => {
    const { text, parse_mode = 'Markdown' } = req.body;
    const result = await telegramRequest('sendMessage', { text, parse_mode });
    res.json(result);
});

// 发送图片
app.post('/api/telegram/sendPhoto', async (req, res) => {
    const { photo, caption } = req.body;
    const result = await telegramRequest('sendPhoto', { photo, caption });
    res.json(result);
});

// 系统状态路由
app.get('/api/status', (req, res) => {
    res.json({
        success: true,
        status: 'running',
        timestamp: new Date().toISOString(),
        version: '1.0.0',
        services: {
            binance: 'connected',
            telegram: 'connected'
        }
    });
});

// 健康检查路由
app.get('/api/health', (req, res) => {
    res.json({
        success: true,
        health: 'ok',
        uptime: process.uptime(),
        memory: process.memoryUsage(),
        timestamp: new Date().toISOString()
    });
});

// 错误处理中间件
app.use((error, req, res, next) => {
    console.error('❌ 服务器错误:', error);
    res.status(500).json({
        success: false,
        error: '服务器内部错误',
        message: error.message
    });
});

// 404处理
app.use((req, res) => {
    res.status(404).json({
        success: false,
        error: '接口不存在',
        path: req.path
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log('🚀 断金交易系统后端服务器启动成功！');
    console.log(`📡 服务器地址: http://localhost:${PORT}`);
    console.log(`🔗 币安API: ${CONFIG.BINANCE.BASE_URL}`);
    console.log(`🤖 Telegram机器人: 已配置`);
    console.log('📊 支持的功能:');
    console.log('   ✅ 币安API代理');
    console.log('   ✅ Telegram消息推送');
    console.log('   ✅ 账户信息查询');
    console.log('   ✅ 实时交易执行');
    console.log('   ✅ 安全密钥管理');
    console.log('');
    console.log('💡 前端访问地址: http://localhost:' + PORT);
    console.log('📝 API文档: http://localhost:' + PORT + '/api/status');
});

// 优雅关闭
process.on('SIGTERM', () => {
    console.log('📴 收到SIGTERM信号，正在关闭服务器...');
    process.exit(0);
});

process.on('SIGINT', () => {
    console.log('📴 收到SIGINT信号，正在关闭服务器...');
    process.exit(0);
});

module.exports = app;
