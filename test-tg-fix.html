<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TG状态修复验证</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .container {
            max-width: 900px;
            margin: 0 auto;
        }
        
        .section {
            background: #222;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .btn {
            background: #00d4aa;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        
        .btn:hover { background: #00b894; }
        .btn.danger { background: #ff6b6b; color: #fff; }
        .btn.warning { background: #f59e0b; color: #000; }
        
        .log-display {
            background: #111;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
            max-height: 300px;
            overflow-y: auto;
            border: 1px solid #333;
        }
        
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
        .info { color: #3b82f6; }
        
        .test-step {
            background: #333;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 4px solid #00d4aa;
        }
        
        .test-step.running {
            border-left-color: #f59e0b;
            background: rgba(245, 158, 11, 0.1);
        }
        
        .test-step.success {
            border-left-color: #10b981;
            background: rgba(16, 185, 129, 0.1);
        }
        
        .test-step.error {
            border-left-color: #ef4444;
            background: rgba(239, 68, 68, 0.1);
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 TG机器人状态修复验证</h1>
        
        <div class="section">
            <h2>🎯 修复目标</h2>
            <p><strong>问题</strong>：刷新网站后TG机器人会自动退出显示登录按钮而不是已连接</p>
            <p><strong>解决方案</strong>：</p>
            <ul>
                <li>✅ 在main.js初始化时主动调用restoreTelegramStatus()</li>
                <li>✅ 移除telegram-bot.js中的重复初始化</li>
                <li>✅ 增强调试日志跟踪状态恢复过程</li>
                <li>✅ 确保UI更新函数正确导出到全局作用域</li>
            </ul>
        </div>
        
        <div class="section">
            <h2>🧪 测试步骤</h2>
            <div id="testSteps">
                <div class="test-step" id="step1">1. 清除现有TG状态</div>
                <div class="test-step" id="step2">2. 模拟TG连接并保存状态</div>
                <div class="test-step" id="step3">3. 刷新页面测试状态恢复</div>
                <div class="test-step" id="step4">4. 验证UI显示是否正确</div>
                <div class="test-step" id="step5">5. 检查控制台日志</div>
            </div>
            
            <div>
                <button class="btn" onclick="runTest()">🚀 开始测试</button>
                <button class="btn warning" onclick="setupTGStatus()">⚙️ 设置TG已连接状态</button>
                <button class="btn danger" onclick="clearTGStatus()">🗑️ 清除TG状态</button>
                <button class="btn" onclick="location.reload()">🔄 刷新页面</button>
            </div>
        </div>
        
        <div class="section">
            <h2>📊 状态监控</h2>
            <div class="log-display" id="logDisplay">等待测试开始...</div>
        </div>
        
        <div class="section">
            <h2>💾 localStorage状态</h2>
            <div class="log-display" id="storageStatus">检查中...</div>
            <button class="btn" onclick="checkStorage()">🔍 检查存储</button>
        </div>
        
        <div class="section">
            <h2>🔗 快速导航</h2>
            <button class="btn" onclick="location.href='/'">🏠 返回主页</button>
            <button class="btn" onclick="location.href='/test-tg-status.html'">🤖 TG状态测试</button>
            <button class="btn" onclick="openConsole()">🔍 打开控制台</button>
        </div>
    </div>

    <script>
        let testRunning = false;
        
        // 添加日志
        function addLog(message, type = 'info') {
            const display = document.getElementById('logDisplay');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = `[${timestamp}] ${message}\n`;
            display.innerHTML += `<span class="${type}">${logEntry}</span>`;
            display.scrollTop = display.scrollHeight;
            console.log(message);
        }
        
        // 更新测试步骤状态
        function updateStep(stepId, status) {
            const step = document.getElementById(stepId);
            step.className = `test-step ${status}`;
        }
        
        // 设置TG已连接状态
        function setupTGStatus() {
            const tgStatus = {
                connected: true,
                connectionTime: new Date().toISOString()
            };
            
            localStorage.setItem('telegramStatus', JSON.stringify(tgStatus));
            addLog('✅ TG连接状态已设置并保存到localStorage', 'success');
            checkStorage();
        }
        
        // 清除TG状态
        function clearTGStatus() {
            localStorage.removeItem('telegramStatus');
            addLog('🗑️ TG状态已从localStorage清除', 'warning');
            checkStorage();
        }
        
        // 检查存储状态
        function checkStorage() {
            const display = document.getElementById('storageStatus');
            const tgStatus = localStorage.getItem('telegramStatus');
            
            if (tgStatus) {
                try {
                    const parsed = JSON.parse(tgStatus);
                    display.innerHTML = `
<span class="success">✅ TG状态存在</span>
Connected: ${parsed.connected}
Connection Time: ${parsed.connectionTime || '未设置'}
Raw JSON: ${tgStatus}
                    `;
                    addLog(`📥 localStorage中的TG状态: connected=${parsed.connected}`, 'info');
                } catch (error) {
                    display.innerHTML = `<span class="error">❌ TG状态解析失败: ${error.message}</span>`;
                    addLog(`❌ TG状态解析失败: ${error.message}`, 'error');
                }
            } else {
                display.innerHTML = `<span class="warning">⚠️ localStorage中无TG状态</span>`;
                addLog('⚠️ localStorage中未找到TG状态', 'warning');
            }
        }
        
        // 运行完整测试
        async function runTest() {
            if (testRunning) return;
            testRunning = true;
            
            addLog('🚀 开始TG状态修复验证测试', 'info');
            
            // 步骤1：清除现有状态
            updateStep('step1', 'running');
            clearTGStatus();
            await sleep(1000);
            updateStep('step1', 'success');
            
            // 步骤2：设置连接状态
            updateStep('step2', 'running');
            setupTGStatus();
            await sleep(1000);
            updateStep('step2', 'success');
            
            // 步骤3：提示刷新页面
            updateStep('step3', 'running');
            addLog('🔄 请手动刷新页面来测试状态恢复...', 'warning');
            addLog('💡 刷新后检查主页面的TG按钮是否显示"已连接"', 'info');
            
            // 步骤4和5将在页面刷新后验证
            addLog('📋 测试准备完成，请刷新页面验证修复效果', 'success');
            
            testRunning = false;
        }
        
        // 辅助函数
        function sleep(ms) {
            return new Promise(resolve => setTimeout(resolve, ms));
        }
        
        function openConsole() {
            addLog('💡 请按F12打开浏览器开发者工具查看控制台日志', 'info');
        }
        
        // 页面加载时检查状态
        document.addEventListener('DOMContentLoaded', function() {
            addLog('🎯 TG状态修复验证页面已加载', 'info');
            checkStorage();
            
            // 检查是否是刷新后的验证
            const tgStatus = localStorage.getItem('telegramStatus');
            if (tgStatus) {
                try {
                    const parsed = JSON.parse(tgStatus);
                    if (parsed.connected) {
                        addLog('🔍 检测到已保存的TG连接状态，这是刷新后的验证', 'info');
                        updateStep('step3', 'success');
                        updateStep('step4', 'running');
                        
                        // 提示用户检查主页面
                        setTimeout(() => {
                            addLog('✅ 请访问主页面检查TG按钮是否显示"已连接"状态', 'success');
                            updateStep('step4', 'success');
                            updateStep('step5', 'running');
                            addLog('🔍 请检查浏览器控制台是否有TG状态恢复的调试日志', 'info');
                            updateStep('step5', 'success');
                        }, 2000);
                    }
                } catch (error) {
                    addLog(`❌ TG状态解析失败: ${error.message}`, 'error');
                }
            }
        });
        
        console.log('🔧 TG状态修复验证页面已加载');
    </script>
</body>
</html>
