// 测试币安API连接的脚本
const fetch = require('node-fetch');

async function testBinanceAPI() {
    const API_KEY = 'your_api_key_here';  // 请替换为您的真实API Key
    const API_SECRET = 'your_api_secret_here';  // 请替换为您的真实API Secret
    
    try {
        console.log('🧪 测试币安API连接...');
        
        // 1. 设置API配置
        console.log('📝 设置API配置...');
        const configResponse = await fetch('http://localhost:3001/api/binance/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                apiKey: API_KEY,
                apiSecret: API_SECRET,
                useTestnet: false
            })
        });
        
        const configResult = await configResponse.json();
        console.log('配置结果:', configResult);
        
        if (!configResult.success) {
            throw new Error('API配置失败: ' + configResult.error);
        }
        
        // 2. 测试服务器时间
        console.log('⏰ 测试服务器时间...');
        const timeResponse = await fetch('http://localhost:3001/api/binance/time');
        const timeResult = await timeResponse.json();
        console.log('服务器时间:', timeResult);
        
        // 3. 测试现货账户信息
        console.log('💰 测试现货账户信息...');
        const accountResponse = await fetch('http://localhost:3001/api/binance/account');
        const accountResult = await accountResponse.json();
        console.log('现货账户:', accountResult);
        
        // 4. 测试杠杆账户信息
        console.log('⚡ 测试杠杆账户信息...');
        const marginResponse = await fetch('http://localhost:3001/api/binance/margin/account');
        const marginResult = await marginResponse.json();
        console.log('杠杆账户:', marginResult);
        
        console.log('✅ 所有测试完成！');
        
    } catch (error) {
        console.error('❌ 测试失败:', error);
    }
}

// 运行测试
testBinanceAPI();
