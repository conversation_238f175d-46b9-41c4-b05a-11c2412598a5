<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TG机器人状态测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            max-width: 800px;
            margin: 0 auto;
        }
        
        .test-section {
            background: #222;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .btn {
            background: #00d4aa;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        
        .btn:hover {
            background: #00b894;
        }
        
        .btn.danger {
            background: #ff6b6b;
            color: #fff;
        }
        
        .status-display {
            background: #333;
            padding: 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-family: monospace;
            white-space: pre-wrap;
        }
        
        .success { color: #10b981; }
        .error { color: #ef4444; }
        .warning { color: #f59e0b; }
        .info { color: #3b82f6; }
        
        /* 模拟TG按钮样式 */
        .telegram-btn {
            background: linear-gradient(135deg, #229ED9 0%, #0088cc 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            font-weight: 500;
            transition: all 0.3s ease;
            margin: 10px 0;
        }
        
        .telegram-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(34, 158, 217, 0.3);
        }
        
        .telegram-btn.connected {
            background: linear-gradient(135deg, #10b981 0%, #059669 100%);
        }
        
        .status-light {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            display: inline-block;
        }
        
        .status-light.connected {
            background: #10b981;
            box-shadow: 0 0 6px #10b981;
        }
        
        .status-light.disconnected {
            background: #ef4444;
            box-shadow: 0 0 6px #ef4444;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>🤖 TG机器人状态测试</h1>
        
        <div class="test-section">
            <h2>问题描述</h2>
            <p>🔍 <strong>问题</strong>：刷新网站后TG机器人会自动退出显示登录按钮而不是已连接</p>
            <p>🎯 <strong>目标</strong>：修复UI显示，确保刷新后正确恢复TG连接状态</p>
        </div>
        
        <div class="test-section">
            <h2>模拟TG按钮</h2>
            <button class="telegram-btn" id="mockTelegramBtn">
                <span class="status-light disconnected" id="mockStatusLight"></span>
                <span id="mockStatusText">登录</span>
            </button>
            
            <div>
                <button class="btn" onclick="simulateConnect()">模拟连接</button>
                <button class="btn" onclick="simulateDisconnect()">模拟断开</button>
                <button class="btn" onclick="testRestore()">测试状态恢复</button>
                <button class="btn danger" onclick="clearStorage()">清除存储</button>
            </div>
        </div>
        
        <div class="test-section">
            <h2>状态监控</h2>
            <div class="status-display" id="statusDisplay">等待测试...</div>
        </div>
        
        <div class="test-section">
            <h2>localStorage检查</h2>
            <div class="status-display" id="storageDisplay">检查中...</div>
            <button class="btn" onclick="checkStorage()">刷新存储状态</button>
        </div>
        
        <div class="test-section">
            <h2>自动测试</h2>
            <button class="btn" onclick="runAutoTest()">运行完整测试</button>
            <div class="status-display" id="autoTestResult">等待测试...</div>
        </div>
        
        <div class="test-section">
            <button class="btn" onclick="location.href='/'">返回主页测试</button>
            <button class="btn" onclick="location.reload()">刷新页面测试</button>
        </div>
    </div>

    <script>
        let mockTelegramStatus = {
            connected: false,
            connectionTime: null
        };
        
        // 更新状态显示
        function updateStatusDisplay(message, type = 'info') {
            const display = document.getElementById('statusDisplay');
            const timestamp = new Date().toLocaleTimeString();
            const newMessage = `[${timestamp}] ${message}\n`;
            display.innerHTML += `<span class="${type}">${newMessage}</span>`;
            display.scrollTop = display.scrollHeight;
            console.log(message);
        }
        
        // 更新模拟TG按钮UI
        function updateMockTelegramUI(isConnected) {
            const btn = document.getElementById('mockTelegramBtn');
            const light = document.getElementById('mockStatusLight');
            const text = document.getElementById('mockStatusText');
            
            if (isConnected) {
                btn.classList.add('connected');
                light.className = 'status-light connected';
                text.textContent = '已连接';
                updateStatusDisplay('✅ 模拟TG按钮UI已更新为已连接状态', 'success');
            } else {
                btn.classList.remove('connected');
                light.className = 'status-light disconnected';
                text.textContent = '登录';
                updateStatusDisplay('🔄 模拟TG按钮UI已更新为未连接状态', 'warning');
            }
        }
        
        // 模拟连接
        function simulateConnect() {
            mockTelegramStatus.connected = true;
            mockTelegramStatus.connectionTime = new Date().toISOString();
            
            // 保存到localStorage
            localStorage.setItem('telegramStatus', JSON.stringify(mockTelegramStatus));
            
            updateMockTelegramUI(true);
            updateStatusDisplay('🤖 模拟TG连接成功，状态已保存', 'success');
            checkStorage();
        }
        
        // 模拟断开
        function simulateDisconnect() {
            mockTelegramStatus.connected = false;
            mockTelegramStatus.connectionTime = null;
            
            // 保存到localStorage
            localStorage.setItem('telegramStatus', JSON.stringify(mockTelegramStatus));
            
            updateMockTelegramUI(false);
            updateStatusDisplay('🔌 模拟TG断开连接，状态已保存', 'warning');
            checkStorage();
        }
        
        // 测试状态恢复
        function testRestore() {
            updateStatusDisplay('🔄 开始测试状态恢复...', 'info');
            
            try {
                const savedStatus = localStorage.getItem('telegramStatus');
                if (savedStatus) {
                    const parsed = JSON.parse(savedStatus);
                    updateStatusDisplay(`📥 从localStorage恢复状态: connected=${parsed.connected}`, 'info');
                    
                    if (parsed.connected) {
                        mockTelegramStatus = parsed;
                        updateMockTelegramUI(true);
                        updateStatusDisplay('✅ TG连接状态恢复成功', 'success');
                    } else {
                        updateMockTelegramUI(false);
                        updateStatusDisplay('⚠️ TG状态为未连接', 'warning');
                    }
                } else {
                    updateStatusDisplay('❌ 未找到保存的TG状态', 'error');
                    updateMockTelegramUI(false);
                }
            } catch (error) {
                updateStatusDisplay(`❌ 状态恢复失败: ${error.message}`, 'error');
            }
        }
        
        // 检查存储状态
        function checkStorage() {
            const display = document.getElementById('storageDisplay');
            const savedStatus = localStorage.getItem('telegramStatus');
            
            if (savedStatus) {
                try {
                    const parsed = JSON.parse(savedStatus);
                    display.innerHTML = `
<span class="success">✅ TG状态已保存</span>
Connected: ${parsed.connected}
Connection Time: ${parsed.connectionTime || '未设置'}
Raw Data: ${savedStatus}
                    `;
                } catch (error) {
                    display.innerHTML = `<span class="error">❌ 状态解析失败: ${error.message}</span>`;
                }
            } else {
                display.innerHTML = `<span class="warning">⚠️ 未找到保存的TG状态</span>`;
            }
        }
        
        // 清除存储
        function clearStorage() {
            localStorage.removeItem('telegramStatus');
            mockTelegramStatus = { connected: false, connectionTime: null };
            updateMockTelegramUI(false);
            updateStatusDisplay('🗑️ localStorage已清除', 'warning');
            checkStorage();
        }
        
        // 运行自动测试
        async function runAutoTest() {
            const result = document.getElementById('autoTestResult');
            result.innerHTML = '<span class="info">🔄 运行自动测试...</span>\n';
            
            // 测试步骤
            const tests = [
                { name: '清除存储', action: clearStorage },
                { name: '模拟连接', action: simulateConnect },
                { name: '检查存储', action: checkStorage },
                { name: '模拟断开', action: simulateDisconnect },
                { name: '重新连接', action: simulateConnect },
                { name: '测试恢复', action: testRestore }
            ];
            
            for (let i = 0; i < tests.length; i++) {
                const test = tests[i];
                result.innerHTML += `<span class="info">${i + 1}. ${test.name}...</span>\n`;
                test.action();
                await new Promise(resolve => setTimeout(resolve, 1000));
            }
            
            result.innerHTML += '<span class="success">✅ 自动测试完成</span>\n';
        }
        
        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateStatusDisplay('🎯 TG状态测试页面已加载', 'info');
            checkStorage();
            testRestore();
        });
        
        console.log('🤖 TG机器人状态测试页面已加载');
    </script>
</body>
</html>
