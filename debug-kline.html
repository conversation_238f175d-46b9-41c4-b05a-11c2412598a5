<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>K线图调试页面</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .debug-container {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .kline-test-container {
            width: 100%;
            height: 400px;
            background: #1a1a1a;
            border: 1px solid #333;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .debug-info {
            background: #222;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
        }
        
        .test-btn {
            background: #00d4aa;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        
        .test-btn:hover {
            background: #00b894;
        }
    </style>
</head>
<body>
    <div class="debug-container">
        <h1>🔧 K线图调试页面</h1>
        
        <div class="debug-info">
            <h3>📊 测试控制</h3>
            <button class="test-btn" onclick="testKlineInit()">初始化K线图</button>
            <button class="test-btn" onclick="testMockData()">测试模拟数据</button>
            <button class="test-btn" onclick="testCanvasResize()">测试Canvas调整</button>
            <button class="test-btn" onclick="clearKlineChart()">清空图表</button>
        </div>
        
        <div class="debug-info">
            <h3>📈 K线图容器</h3>
            <div class="kline-test-container" id="debugKlineContainer">
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                    等待初始化...
                </div>
            </div>
        </div>
        
        <div class="debug-info" id="debugLog">
            <h3>🔍 调试日志</h3>
            <div id="logContent" style="font-family: monospace; font-size: 12px; max-height: 200px; overflow-y: auto;">
                准备开始调试...
            </div>
        </div>
    </div>

    <!-- 加载必要的JS文件 -->
    <script src="js/kline-advanced.js"></script>
    
    <script>
        // 调试日志函数
        function debugLog(message) {
            const logContent = document.getElementById('logContent');
            const timestamp = new Date().toLocaleTimeString();
            logContent.innerHTML += `<br>[${timestamp}] ${message}`;
            logContent.scrollTop = logContent.scrollHeight;
            console.log(message);
        }
        
        // 测试K线图初始化
        async function testKlineInit() {
            debugLog('🚀 开始测试K线图初始化...');
            
            try {
                if (typeof window.initializeAdvancedKlineChart === 'function') {
                    debugLog('✅ K线图函数已加载');
                    await window.initializeAdvancedKlineChart('debugKlineContainer', 'BTCUSDT');
                    debugLog('✅ K线图初始化完成');
                } else {
                    debugLog('❌ K线图函数未找到');
                }
            } catch (error) {
                debugLog('❌ K线图初始化失败: ' + error.message);
            }
        }
        
        // 测试模拟数据
        function testMockData() {
            debugLog('📊 测试模拟数据生成...');
            
            try {
                if (typeof window.generateMockKlineData === 'function') {
                    const mockData = window.generateMockKlineData('BTCUSDT');
                    debugLog(`✅ 生成模拟数据: ${mockData.length} 条记录`);
                    debugLog(`📈 价格范围: $${Math.min(...mockData.map(d => d.low)).toFixed(2)} - $${Math.max(...mockData.map(d => d.high)).toFixed(2)}`);
                    
                    // 尝试绘制
                    if (window.KLINE_CONFIG && window.KLINE_CONFIG.canvas) {
                        debugLog('🎨 开始绘制模拟数据...');
                        // 这里需要调用绘制函数
                    }
                } else {
                    debugLog('❌ 模拟数据函数未找到');
                }
            } catch (error) {
                debugLog('❌ 模拟数据测试失败: ' + error.message);
            }
        }
        
        // 测试Canvas调整
        function testCanvasResize() {
            debugLog('📐 测试Canvas尺寸调整...');
            
            try {
                if (window.KLINE_CONFIG && window.KLINE_CONFIG.canvas) {
                    const canvas = window.KLINE_CONFIG.canvas;
                    debugLog(`📏 当前Canvas尺寸: ${canvas.width}x${canvas.height}`);
                    debugLog(`📦 容器尺寸: ${canvas.parentElement.clientWidth}x${canvas.parentElement.clientHeight}`);
                } else {
                    debugLog('❌ Canvas未找到');
                }
            } catch (error) {
                debugLog('❌ Canvas调整测试失败: ' + error.message);
            }
        }
        
        // 清空图表
        function clearKlineChart() {
            debugLog('🧹 清空K线图...');
            
            const container = document.getElementById('debugKlineContainer');
            container.innerHTML = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; color: #666;">
                    图表已清空
                </div>
            `;
            
            debugLog('✅ 图表已清空');
        }
        
        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', function() {
            debugLog('📄 调试页面加载完成');
            debugLog('🔍 检查K线图模块...');
            
            setTimeout(() => {
                if (typeof window.initializeAdvancedKlineChart === 'function') {
                    debugLog('✅ K线图模块已加载');
                } else {
                    debugLog('❌ K线图模块未加载');
                }
            }, 1000);
        });
    </script>
</body>
</html>
