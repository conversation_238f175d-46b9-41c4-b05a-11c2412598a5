<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>调试页面</title>
    <style>
        body { background: #000; color: #0f0; font-family: monospace; padding: 20px; }
        .error { color: #f00; }
        .success { color: #0f0; }
        .warning { color: #ff0; }
    </style>
</head>
<body>
    <h1>🔍 断金交易系统调试页面</h1>
    <div id="debug-output"></div>

    <script>
        const output = document.getElementById('debug-output');
        
        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = type;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            output.appendChild(div);
            console.log(message);
        }

        log('🚀 开始调试检查...', 'success');

        // 检查服务器连接
        fetch('http://localhost:3001/api/health')
            .then(response => response.json())
            .then(data => {
                log('✅ 服务器连接正常', 'success');
                log(`📡 服务器响应: ${JSON.stringify(data)}`, 'info');
            })
            .catch(error => {
                log('❌ 服务器连接失败: ' + error.message, 'error');
            });

        // 逐个加载JavaScript文件并检查
        const scripts = [
            'js/data.js',
            'js/binance-api.js', 
            'js/telegram-bot.js',
            'js/trading.js',
            'js/ui.js',
            'js/matrix.js',
            'js/kline-chart.js',
            'js/main.js'
        ];

        let loadedScripts = 0;

        scripts.forEach((scriptPath, index) => {
            const script = document.createElement('script');
            script.src = scriptPath;
            script.onload = () => {
                loadedScripts++;
                log(`✅ ${scriptPath} 加载成功`, 'success');
                
                if (loadedScripts === scripts.length) {
                    log('🎉 所有脚本加载完成，开始功能检查...', 'success');
                    checkFunctions();
                }
            };
            script.onerror = () => {
                log(`❌ ${scriptPath} 加载失败`, 'error');
            };
            document.head.appendChild(script);
        });

        function checkFunctions() {
            // 检查关键全局变量
            const globalVars = ['cryptos', 'positions', 'tradeHistory', 'accountBalance'];
            globalVars.forEach(varName => {
                if (window[varName]) {
                    log(`✅ 全局变量 ${varName} 存在`, 'success');
                    if (Array.isArray(window[varName])) {
                        log(`📊 ${varName} 长度: ${window[varName].length}`, 'info');
                    }
                } else {
                    log(`❌ 全局变量 ${varName} 不存在`, 'error');
                }
            });

            // 检查关键函数
            const functions = [
                'updateCryptoPrices',
                'getCryptoInfo', 
                'updateBalanceDisplay',
                'fetchSpotAccountInfo',
                'connectBinanceAPI'
            ];
            
            functions.forEach(funcName => {
                if (typeof window[funcName] === 'function') {
                    log(`✅ 函数 ${funcName} 存在`, 'success');
                } else {
                    log(`❌ 函数 ${funcName} 不存在`, 'error');
                }
            });

            // 测试数据获取
            if (window.cryptos && window.cryptos.length > 0) {
                log(`💰 加密货币数据: ${window.cryptos.length} 种`, 'success');
                log(`📈 第一种货币: ${window.cryptos[0].symbol} - ${window.cryptos[0].name}`, 'info');
            } else {
                log('❌ 加密货币数据为空', 'error');
            }
        }

        // 监听错误
        window.addEventListener('error', (event) => {
            log(`❌ JavaScript错误: ${event.error.message}`, 'error');
            log(`📍 文件: ${event.filename}:${event.lineno}`, 'error');
        });
    </script>
</body>
</html>
