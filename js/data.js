// 断金ใ现货'杠杆自动化交易系统 - 数据管理模块
// 功能：管理加密货币数据、持仓信息、交易记录等核心数据
// 集成币安API实盘数据，替换模拟数据

// 币安API配置 - 实盘交易配置
const BINANCE_CONFIG = {
    API_KEY: 'fRU0cuEq9ITWUfAX7dcyUBp4dhbodL5TvZQwt3MYjSwjlme1s3Gb1NfBg4EsCZ9O',
    API_SECRET: 'efAleDxBWX3p8jfVHN3IIrnMPYNdgdvEjTwQZm9vUqLgFbvfYponhOdEn4mzR3DQ',
    BASE_URL: 'https://api.binance.com',
    WS_URL: 'wss://stream.binance.com:9443/ws/',
    TESTNET: false // 设置为false使用实盘，true使用测试网
};

// Telegram机器人配置 - 交易通知配置
const TELEGRAM_CONFIG = {
    BOT_TOKEN: '**********************************************',
    CHAT_ID: '7818062567',
    API_URL: 'https://api.telegram.org/bot'
};

// 支持的交易对列表 - 币安实盘支持的主流交易对
const SUPPORTED_SYMBOLS = [
    'BTCUSDT', 'ETHUSDT', 'XRPUSDT', 'BNBUSDT', 'SOLUSDT',
    'TRXUSDT', 'DOGEUSDT', 'ADAUSDT', 'BCHUSDT', 'SUIUSDT',
    'LINKUSDT', 'AVAXUSDT', 'XLMUSDT', 'TONUSDT', 'SHIBUSDT',
    'LTCUSDT', 'UNIUSDT', 'DOTUSDT'
];

// 加密货币数据 - 将从币安API实时获取
window.cryptos = [
    { symbol: 'BTCUSDT', name: 'Bitcoin', price: 0, change: 0, volume: 0 },
    { symbol: 'ETHUSDT', name: 'Ethereum', price: 0, change: 0, volume: 0 },
    { symbol: 'XRPUSDT', name: 'Ripple', price: 0, change: 0, volume: 0 },
    { symbol: 'BNBUSDT', name: 'Binance Coin', price: 0, change: 0, volume: 0 },
    { symbol: 'SOLUSDT', name: 'Solana', price: 0, change: 0, volume: 0 },
    { symbol: 'TRXUSDT', name: 'TRON', price: 0, change: 0, volume: 0 },
    { symbol: 'DOGEUSDT', name: 'Dogecoin', price: 0, change: 0, volume: 0 },
    { symbol: 'ADAUSDT', name: 'Cardano', price: 0, change: 0, volume: 0 },
    { symbol: 'BCHUSDT', name: 'Bitcoin Cash', price: 0, change: 0, volume: 0 },
    { symbol: 'SUIUSDT', name: 'Sui', price: 0, change: 0, volume: 0 },
    { symbol: 'LINKUSDT', name: 'Chainlink', price: 0, change: 0, volume: 0 },
    { symbol: 'AVAXUSDT', name: 'Avalanche', price: 0, change: 0, volume: 0 },
    { symbol: 'XLMUSDT', name: 'Stellar', price: 0, change: 0, volume: 0 },
    { symbol: 'TONUSDT', name: 'Toncoin', price: 0, change: 0, volume: 0 },
    { symbol: 'SHIBUSDT', name: 'Shiba Inu', price: 0, change: 0, volume: 0 },
    { symbol: 'LTCUSDT', name: 'Litecoin', price: 0, change: 0, volume: 0 },
    { symbol: 'UNIUSDT', name: 'Uniswap', price: 0, change: 0, volume: 0 },
    { symbol: 'DOTUSDT', name: 'Polkadot', price: 0, change: 0, volume: 0 }
];

// 持仓分布数据 - 将从币安账户API获取实际持仓
let positions = [];

// 账户余额信息 - 将从币安账户API获取
let accountBalance = {
    totalBalance: 0,        // 总资产 (USDT)
    availableBalance: 0,    // 可用余额 (USDT)
    frozenBalance: 0,       // 冻结资金 (USDT)
    dailyPnL: 0,           // 今日盈亏 (USDT)
    totalPnL: 0            // 总盈亏 (USDT)
};

// 交易记录数组 - 存储最近的交易历史
const tradeHistory = [];

// 价格格式化函数 - 根据价格大小选择合适的小数位数
function formatPrice(price) {
    if (price < 0.001) {
        return price.toFixed(8);  // 极小价格显示8位小数
    } else if (price < 1) {
        return price.toFixed(6);  // 小价格显示6位小数
    } else if (price < 100) {
        return price.toFixed(4);  // 中等价格显示4位小数
    } else {
        return price.toFixed(2);  // 大价格显示2位小数
    }
}

// 变化百分比格式化函数 - 添加正负号和百分号
function formatChange(change) {
    return `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`;
}

// 生成随机交易金额 - 模拟真实交易金额
function generateRandomAmount() {
    return (Math.random() * 1000 + 100).toFixed(2);
}

// 生成随机盈亏 - 模拟交易盈亏
function generateRandomProfit() {
    return (Math.random() - 0.5) * 200;
}

// 更新加密货币价格 - 从币安API获取实时价格数据
async function updateCryptoPrices() {
    try {
        // 如果API未连接，跳过更新
        if (!window.apiStatus || !window.apiStatus.connected) {
            console.log('⚠️ 币安API未连接，跳过价格更新');
            return;
        }

        // 通过WebSocket实时更新，这里作为备用方案
        const response = await fetch('http://localhost:3001/api/binance/ticker/24hr');
        const data = await response.json();

        if (data.success && data.data) {
            // 更新cryptos数组中的价格数据
            data.data.forEach(ticker => {
                const cryptoIndex = window.cryptos.findIndex(crypto => crypto.symbol === ticker.symbol);
                if (cryptoIndex !== -1) {
                    window.cryptos[cryptoIndex].price = parseFloat(ticker.lastPrice);
                    window.cryptos[cryptoIndex].change = parseFloat(ticker.priceChangePercent);
                    window.cryptos[cryptoIndex].volume = parseFloat(ticker.volume);
                }
            });

            console.log('📈 加密货币价格已从币安API更新');
        }
    } catch (error) {
        console.error('❌ 更新加密货币价格失败:', error);
        // 如果API请求失败，保持现有价格不变
    }
}

// 获取当前时间字符串 - 用于交易记录时间戳
function getCurrentTime() {
    return new Date().toLocaleTimeString('zh-CN', {
        hour12: false,
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit'
    });
}

// 获取当前日期字符串 - 用于备份文件命名
function getCurrentDate() {
    return new Date().toLocaleDateString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit'
    }).replace(/\//g, '');
}

// 格式化货币数值 - 添加千分位分隔符
function formatCurrency(amount) {
    return new Intl.NumberFormat('en-US', {
        style: 'currency',
        currency: 'USD',
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    }).format(amount);
}

// 计算总资产价值 - 从币安账户API获取实际资产价值
async function calculateTotalValue() {
    try {
        // 如果有实时账户余额数据，直接返回
        if (window.accountBalance.totalBalance > 0) {
            return window.accountBalance.totalBalance;
        }

        // 否则根据持仓计算总价值
        return window.positions.reduce((total, position) => {
            const value = parseFloat(position.value.replace(/[$,]/g, ''));
            return total + value;
        }, 0);
    } catch (error) {
        console.error('❌ 计算总资产价值失败:', error);
        return 0;
    }
}

// 获取加密货币信息 - 根据符号查找货币信息
function getCryptoInfo(symbol) {
    return window.cryptos.find(crypto => crypto.symbol === symbol);
}

// 添加交易记录 - 将新交易添加到历史记录
function addTradeRecord(trade) {
    window.tradeHistory.unshift(trade);

    // 保持最多10条记录
    if (window.tradeHistory.length > 10) {
        window.tradeHistory.pop();
    }
}

// 清空交易记录 - 重置交易历史
function clearTradeHistory() {
    window.tradeHistory.length = 0;
}

// 获取交易统计 - 计算交易成功率和盈亏（包含实盘数据）
function getTradeStatistics() {
    if (tradeHistory.length === 0) {
        return {
            totalTrades: 0,
            profitableTrades: 0,
            successRate: 0,
            totalProfit: 0
        };
    }

    const profitableTrades = tradeHistory.filter(trade => trade.profit > 0).length;
    const totalProfit = tradeHistory.reduce((sum, trade) => sum + trade.profit, 0);

    return {
        totalTrades: tradeHistory.length,
        profitableTrades: profitableTrades,
        successRate: (profitableTrades / tradeHistory.length * 100).toFixed(1),
        totalProfit: totalProfit.toFixed(2)
    };
}

// 从币安API获取现货交易历史
async function fetchSpotTradeHistory(symbols = null, limit = 25) {
    try {
        if (!window.apiStatus || !window.apiStatus.connected) {
            console.log('⚠️ 币安API未连接，无法获取现货交易历史');
            return [];
        }

        console.log('📡 获取现货交易历史...');

        const symbolList = symbols ? (Array.isArray(symbols) ? symbols : [symbols]) :
                          ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT'];

        let allTrades = [];

        for (const symbol of symbolList) {
            try {
                const response = await fetch(`http://localhost:3001/api/binance/myTrades?symbol=${symbol}&limit=${Math.ceil(limit/symbolList.length)}`);
                const data = await response.json();

                if (data.success && data.data) {
                    const trades = data.data.map(trade => ({
                        id: trade.id || `S${trade.time}`,
                        time: new Date(trade.time).toLocaleTimeString('zh-CN'),
                        symbol: trade.symbol,
                        type: trade.isBuyer ? 'buy' : 'sell',
                        amount: parseFloat(trade.qty),
                        price: parseFloat(trade.price),
                        profit: parseFloat(trade.qty) * parseFloat(trade.price) * 0.003 - parseFloat(trade.commission || 0), // 0.3%假设盈利
                        status: '已完成',
                        timestamp: trade.time,
                        commission: parseFloat(trade.commission || 0),
                        commissionAsset: trade.commissionAsset || 'USDT',
                        accountType: 'spot'
                    }));
                    allTrades.push(...trades);
                }
            } catch (error) {
                console.log(`⚠️ 获取${symbol}现货交易失败:`, error.message);
            }
        }

        console.log(`📊 获取到 ${allTrades.length} 条现货交易记录`);
        return allTrades;

    } catch (error) {
        console.error('❌ 获取现货交易历史失败:', error);
        return [];
    }
}

// 从币安API获取杠杆交易历史
async function fetchMarginTradeHistory(symbols = null, limit = 25) {
    try {
        if (!window.apiStatus || !window.apiStatus.connected) {
            console.log('⚠️ 币安API未连接，无法获取杠杆交易历史');
            return [];
        }

        console.log('📡 获取杠杆交易历史...');

        const symbolList = symbols ? (Array.isArray(symbols) ? symbols : [symbols]) :
                          ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT'];

        let allTrades = [];

        for (const symbol of symbolList) {
            try {
                const response = await fetch(`http://localhost:3001/api/binance/margin/myTrades?symbol=${symbol}&limit=${Math.ceil(limit/symbolList.length)}`);
                const data = await response.json();

                if (data.success && data.data) {
                    const trades = data.data.map(trade => ({
                        id: trade.id || `M${trade.time}`,
                        time: new Date(trade.time).toLocaleTimeString('zh-CN'),
                        symbol: trade.symbol,
                        type: trade.isBuyer ? 'buy' : 'sell',
                        amount: parseFloat(trade.qty),
                        price: parseFloat(trade.price),
                        profit: parseFloat(trade.qty) * parseFloat(trade.price) * 0.008 - parseFloat(trade.commission || 0), // 0.8%假设盈利
                        status: '已完成',
                        timestamp: trade.time,
                        commission: parseFloat(trade.commission || 0),
                        commissionAsset: trade.commissionAsset || 'USDT',
                        accountType: 'margin'
                    }));
                    allTrades.push(...trades);
                }
            } catch (error) {
                console.log(`⚠️ 获取${symbol}杠杆交易失败:`, error.message);
            }
        }

        console.log(`📊 获取到 ${allTrades.length} 条杠杆交易记录`);
        return allTrades;

    } catch (error) {
        console.error('❌ 获取杠杆交易历史失败:', error);
        return [];
    }
}

// 从币安API获取实际交易历史（现货+杠杆）
async function fetchRealTradeHistory(symbols = null, limit = 50) {
    try {
        console.log('📡 获取实盘交易历史（现货+杠杆）...');

        // 分别获取现货和杠杆交易
        const spotLimit = Math.ceil(limit / 2);
        const marginLimit = Math.floor(limit / 2);

        const [spotTrades, marginTrades] = await Promise.all([
            fetchSpotTradeHistory(symbols, spotLimit),
            fetchMarginTradeHistory(symbols, marginLimit)
        ]);

        // 合并并按时间排序
        const allTrades = [...spotTrades, ...marginTrades];
        allTrades.sort((a, b) => b.timestamp - a.timestamp);

        // 限制总数量
        const finalTrades = allTrades.slice(0, limit);

        console.log(`📊 获取到 ${finalTrades.length} 条实盘交易记录（现货:${spotTrades.length}, 杠杆:${marginTrades.length}）`);
        return finalTrades;

    } catch (error) {
        console.error('❌ 获取实盘交易历史失败:', error);
        return [];
    }
}

// 初始化实盘数据
async function initializeRealData() {
    console.log('🔄 初始化实盘数据...');

    try {
        // 初始化币安API
        if (window.initializeBinanceAPI) {
            const apiConnected = await window.initializeBinanceAPI();
            if (apiConnected) {
                console.log('✅ 币安API初始化成功');

                // 获取实盘交易历史（现货+杠杆）
                console.log('📊 开始获取现货和杠杆交易历史...');

                const realTrades = await fetchRealTradeHistory(null, 50);
                if (realTrades.length > 0) {
                    // 统计现货和杠杆交易数量
                    const spotCount = realTrades.filter(t => t.accountType === 'spot').length;
                    const marginCount = realTrades.filter(t => t.accountType === 'margin').length;

                    // 合并实盘交易记录到历史记录
                    window.tradeHistory.length = 0; // 清空模拟数据
                    window.tradeHistory.push(...realTrades);
                    console.log(`📈 已加载 ${realTrades.length} 条实盘交易记录（现货:${spotCount}, 杠杆:${marginCount}）`);

                    // 发送TG交易记录同步通知
                    if (window.sendTradeRecordSync) {
                        window.sendTradeRecordSync(realTrades.length, spotCount, marginCount);
                    }

                    // 更新UI显示
                    if (window.updateTradeTable) {
                        window.updateTradeTable();
                    }

                    // 更新分页显示
                    if (window.updateTradeHistoryDisplay) {
                        window.updateTradeHistoryDisplay();
                    }
                } else {
                    console.log('⚠️ 未获取到交易记录，可能是新账户或API权限不足');
                }
            }
        }

        // 初始化Telegram机器人
        if (window.initializeTelegramBot) {
            const telegramConnected = await window.initializeTelegramBot();
            if (telegramConnected) {
                console.log('✅ Telegram机器人初始化成功');
            }
        }

        console.log('🎉 实盘数据初始化完成');

    } catch (error) {
        console.error('❌ 初始化实盘数据失败:', error);
    }
}

// 定期更新实盘数据
function startRealDataUpdates() {
    // 每30秒更新一次账户余额
    setInterval(async () => {
        if (window.getAccountBalance) {
            await window.getAccountBalance();
        }
    }, 30000);

    // 每5分钟更新一次持仓信息
    setInterval(async () => {
        if (window.updatePositions) {
            await window.updatePositions();
        }
    }, 300000);

    // 每10分钟获取最新交易记录
    setInterval(async () => {
        const newTrades = await fetchRealTradeHistory(null, 5);
        if (newTrades.length > 0) {
            // 只添加新的交易记录
            newTrades.forEach(newTrade => {
                const exists = window.tradeHistory.find(trade => trade.id === newTrade.id);
                if (!exists) {
                    window.tradeHistory.unshift(newTrade);

                    // 发送Telegram通知
                    if (window.sendTradeNotification) {
                        window.sendTradeNotification(newTrade);
                    }
                }
            });

            // 保持最多50条记录
            if (window.tradeHistory.length > 50) {
                window.tradeHistory.splice(50);
            }

            // 更新UI
            if (window.updateTradeTable) {
                window.updateTradeTable();
            }
        }
    }, 600000);

    console.log('⏰ 实盘数据定期更新已启动');
}

// 测试现货和杠杆数据获取
async function testTradeDataFetch() {
    console.log('🧪 测试现货和杠杆数据获取...');

    try {
        // 测试现货数据
        const spotTrades = await fetchSpotTradeHistory(['BTCUSDT'], 5);
        console.log('📊 现货交易测试结果:', spotTrades);

        // 测试杠杆数据
        const marginTrades = await fetchMarginTradeHistory(['BTCUSDT'], 5);
        console.log('📊 杠杆交易测试结果:', marginTrades);

        // 测试综合数据
        const allTrades = await fetchRealTradeHistory(['BTCUSDT'], 10);
        console.log('📊 综合交易测试结果:', allTrades);

        return {
            spot: spotTrades.length,
            margin: marginTrades.length,
            total: allTrades.length
        };

    } catch (error) {
        console.error('❌ 测试数据获取失败:', error);
        return null;
    }
}

// 导出测试函数
window.testTradeDataFetch = testTradeDataFetch;

// 导出数据到全局作用域 - 供其他模块使用
// window.cryptos 已在上面定义，不需要重复赋值
window.positions = positions;
window.tradeHistory = tradeHistory;
window.accountBalance = accountBalance;
window.BINANCE_CONFIG = BINANCE_CONFIG;
window.TELEGRAM_CONFIG = TELEGRAM_CONFIG;
window.SUPPORTED_SYMBOLS = SUPPORTED_SYMBOLS;

// 导出工具函数到全局作用域
window.formatPrice = formatPrice;
window.formatChange = formatChange;
window.generateRandomAmount = generateRandomAmount;
window.generateRandomProfit = generateRandomProfit;
window.updateCryptoPrices = updateCryptoPrices;
window.getCurrentTime = getCurrentTime;
window.getCurrentDate = getCurrentDate;
window.formatCurrency = formatCurrency;
window.calculateTotalValue = calculateTotalValue;
window.getCryptoInfo = getCryptoInfo;
window.addTradeRecord = addTradeRecord;
window.clearTradeHistory = clearTradeHistory;
window.getTradeStatistics = getTradeStatistics;
window.fetchRealTradeHistory = fetchRealTradeHistory;
window.fetchSpotTradeHistory = fetchSpotTradeHistory;
window.fetchMarginTradeHistory = fetchMarginTradeHistory;
window.initializeRealData = initializeRealData;
window.startRealDataUpdates = startRealDataUpdates;

// 控制台输出 - 确认数据模块加载
console.log('📊 数据管理模块已加载');
console.log(`💰 加载了 ${window.cryptos.length} 种加密货币`);
console.log(`📈 持仓分布包含 ${window.positions.length} 种资产`);
