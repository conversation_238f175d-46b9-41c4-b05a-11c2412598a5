// 断金ใ现货'杠杆自动化交易系统 - Telegram机器人集成模块
// 功能：Telegram通知、交易报告、系统状态推送等
// 安全性：通过后端代理发送消息，保护Bot Token

// Telegram机器人状态 - 支持持久化
let telegramStatus = {
    connected: false,        // 连接状态
    lastMessage: null,       // 最后发送的消息
    messageCount: 0,         // 消息计数
    errorCount: 0,           // 错误计数
    rateLimitRemaining: 30,  // 剩余发送次数（每分钟30条）
    connectionTime: null     // 连接时间
};

// 从localStorage恢复TG连接状态
async function restoreTelegramStatus() {
    console.log('🔄 开始恢复TG机器人连接状态...');

    try {
        const savedStatus = localStorage.getItem('telegramStatus');
        console.log('📥 从localStorage读取TG状态:', savedStatus);

        if (savedStatus) {
            const parsed = JSON.parse(savedStatus);
            console.log('📋 解析TG状态数据:', parsed);

            if (parsed.connected) {
                console.log('✅ 检测到已保存的TG连接状态，开始恢复...');

                // 直接恢复状态，不进行验证（避免API未配置时误判）
                telegramStatus.connected = true;
                telegramStatus.connectionTime = parsed.connectionTime;
                console.log('✅ TG机器人连接状态已恢复');
                console.log('📊 当前TG状态:', telegramStatus);

                // 更新UI状态
                console.log('🔄 更新TG按钮UI状态...');
                updateTelegramConnectionUI(true);

                // 发送重连通知
                setTimeout(() => {
                    console.log('📤 发送TG重连通知...');
                    sendTelegramConnectionNotification(true);
                }, 2000);

                return true;
            } else {
                console.log('⚠️ 保存的TG状态为未连接');
                updateTelegramConnectionUI(false);
                return false;
            }
        } else {
            console.log('⚠️ 未找到保存的TG状态');
            updateTelegramConnectionUI(false);
            return false;
        }
    } catch (error) {
        console.error('❌ 恢复TG状态失败:', error);
        updateTelegramConnectionUI(false);
        // 不清除状态，让用户手动处理
        return false;
    }
}

// 更新TG连接UI状态
function updateTelegramConnectionUI(isConnected) {
    console.log(`🎨 开始更新TG按钮UI状态: ${isConnected ? '已连接' : '未连接'}`);

    const telegramBtn = document.getElementById('telegramLoginBtn');
    const statusLight = document.getElementById('telegramStatusLight');
    const statusText = document.getElementById('telegramStatusText');

    console.log('🔍 查找TG按钮元素:', {
        telegramBtn: !!telegramBtn,
        statusLight: !!statusLight,
        statusText: !!statusText
    });

    if (telegramBtn && statusLight && statusText) {
        if (isConnected) {
            telegramBtn.classList.add('connected');
            statusLight.className = 'status-light connected';
            statusText.textContent = '已连接';
            console.log('✅ TG按钮UI已更新为已连接状态');
            console.log('🎨 按钮类名:', telegramBtn.className);
            console.log('💡 状态灯类名:', statusLight.className);
            console.log('📝 状态文本:', statusText.textContent);
        } else {
            telegramBtn.classList.remove('connected');
            statusLight.className = 'status-light disconnected';
            statusText.textContent = '登录';
            console.log('🔄 TG按钮UI已更新为未连接状态');
        }
    } else {
        console.warn('⚠️ TG按钮元素未找到，可能页面还未完全加载');
        console.warn('🔍 元素查找结果:', {
            telegramLoginBtn: document.getElementById('telegramLoginBtn'),
            telegramStatusLight: document.getElementById('telegramStatusLight'),
            telegramStatusText: document.getElementById('telegramStatusText')
        });
    }
}

// 保存TG连接状态到localStorage
function saveTelegramStatus() {
    try {
        localStorage.setItem('telegramStatus', JSON.stringify({
            connected: telegramStatus.connected,
            connectionTime: telegramStatus.connectionTime
        }));
    } catch (error) {
        console.error('❌ 保存TG状态失败:', error);
    }
}

// 实时数据缓存 - 用于智能通知
let marketDataCache = {
    priceHistory: new Map(),     // symbol -> 价格历史数组
    supportLevels: new Map(),    // symbol -> 支撑位数组
    resistanceLevels: new Map(), // symbol -> 阻力位数组
    lastNotification: new Map(), // 防止重复通知
    volumeSpikes: new Map()      // 成交量异常监控
};

// 通知类型扩展
const NOTIFICATION_TYPES = {
    TRADE_EXECUTED: 'trade_executed',
    BALANCE_UPDATE: 'balance_update',
    PRICE_ALERT: 'price_alert',
    SYSTEM_STATUS: 'system_status',
    ERROR_ALERT: 'error_alert',
    SUPPORT_RESISTANCE: 'support_resistance',
    STRATEGY_SIGNAL: 'strategy_signal',
    CONNECTION_STATUS: 'connection_status',
    VOLUME_SPIKE: 'volume_spike',
    TECHNICAL_ANALYSIS: 'technical_analysis'
};

// 消息模板
const MESSAGE_TEMPLATES = {
    // 系统启动消息
    SYSTEM_START: `
🚀 *断金交易系统启动*

📅 启动时间: {timestamp}
💰 支持币种: {cryptoCount}种
🔗 API状态: {apiStatus}
📊 账户余额: ${accountBalance.totalBalance.toFixed(2)} USDT

系统已准备就绪，开始监控市场...
    `,
    
    // 交易执行通知 - 基于实盘数据
    TRADE_EXECUTED: `
🔥 *交易执行通知*

📊 交易对: {symbol}
💰 类型: {accountType} {tradeType}
📈 价格: ${getCurrentPrice('{symbol}')} USDT
💎 数量: {amount}
💵 总价值: {totalValue} USDT
🎯 策略: {strategy}
⏰ 时间: {timestamp}

📍 技术位置:
• 支撑位: {supportLevel1} / {supportLevel2}
• 阻力位: {resistanceLevel1} / {resistanceLevel2}
• RSI: {rsi}
• 成交量: {volumeChange}

💰 账户状态:
• 现货余额: {spotBalance} USDT
• 杠杆余额: {marginBalance} USDT
• 风险等级: {riskLevel}
    `,
    
    // 账户余额报告
    BALANCE_REPORT: `
📊 *账户余额报告*

💰 总资产: {totalBalance} USDT
💵 可用余额: {availableBalance} USDT
🔒 冻结资金: {frozenBalance} USDT
📈 今日盈亏: {dailyPnL} USDT

⏰ 更新时间: {timestamp}
    `,
    
    // 系统警告
    SYSTEM_WARNING: `
⚠️ *系统警告*

🚨 警告类型: {warningType}
📝 详细信息: {details}
⏰ 发生时间: {timestamp}

请及时检查系统状态！
    `,
    
    // 每日交易总结
    DAILY_SUMMARY: `
📈 *每日交易总结*

📅 日期: {date}
💰 交易次数: {tradeCount}
📊 成功率: {successRate}%
💵 总盈亏: {totalPnL} USDT
🏆 最佳交易: {bestTrade}

明日继续加油！💪
    `,

    // 支撑阻力位通知 - 新增
    SUPPORT_RESISTANCE_ALERT: `
🎯 *技术分析警报*

📊 交易对: {symbol}
💲 当前价格: {currentPrice} USDT
📍 触发位置: {levelType} {levelPrice} USDT

🔍 技术分析:
• 第一支撑: {support1} USDT
• 第二支撑: {support2} USDT
• 第一阻力: {resistance1} USDT
• 第二阻力: {resistance2} USDT

📈 建议操作: {suggestion}
⚡ 强度评分: {strength}/10
🕐 时间框架: {timeframe}
⏰ 时间: {timestamp}
    `,

    // 策略信号通知 - 新增
    STRATEGY_SIGNAL: `
⚡ *策略信号*

🎯 策略: {strategyName}
📊 交易对: {symbol}
⏰ 时间框架: {timeframe}
💲 当前价格: {currentPrice} USDT

📈 信号详情:
• 信号类型: {signalType}
• 强度: {signalStrength}/10
• 入场价: {entryPrice} USDT
• 止损价: {stopLoss} USDT
• 目标价: {targetPrice} USDT

🔍 技术指标:
• MA趋势: {maTrend}
• RSI: {rsi}
• MACD: {macd}
• 成交量: {volumeStatus}

⏰ 时间: {timestamp}
    `,

    // 连接状态通知 - 新增
    CONNECTION_STATUS: `
🔗 *连接状态更新*

📡 API连接: {apiStatus}
🌐 WebSocket: {wsStatus}
📊 数据更新: {dataStatus}
⚡ 延迟: {latency}ms

🔄 重连次数: {reconnectCount}
📈 成功率: {successRate}%
⏰ 时间: {timestamp}

{statusMessage}
    `,

    // 成交量异常通知 - 新增
    VOLUME_SPIKE: `
📊 *成交量异常警报*

🚀 交易对: {symbol}
💲 当前价格: {currentPrice} USDT
📈 成交量激增: +{volumeIncrease}%

🔍 分析:
• 平均成交量: {avgVolume}
• 当前成交量: {currentVolume}
• 异常倍数: {volumeMultiple}x

⚡ 可能原因: {possibleReason}
📈 建议关注: {suggestion}
⏰ 时间: {timestamp}
    `
};

// 初始化Telegram机器人
async function initializeTelegramBot() {
    console.log('🤖 初始化Telegram机器人...');
    
    try {
        // 测试机器人连接
        const botInfo = await getBotInfo();
        if (botInfo) {
            telegramStatus.connected = true;
            telegramStatus.connectionTime = new Date().toISOString();
            console.log('✅ Telegram机器人连接成功');
            console.log('🤖 机器人信息:', botInfo.username);

            // 保存连接状态
            saveTelegramStatus();

            // 更新UI状态
            updateTelegramConnectionUI(true);

            // 发送连接成功通知
            await sendTelegramConnectionNotification(false);

            return true;
        }
    } catch (error) {
        console.error('❌ Telegram机器人连接失败:', error);
        telegramStatus.connected = false;
        telegramStatus.errorCount++;
        saveTelegramStatus(); // 保存失败状态
    }
    
    return false;
}

// 获取机器人信息
async function getBotInfo() {
    try {
        const response = await fetch('/api/telegram/getMe');
        const data = await response.json();
        
        if (data.success) {
            return data.result;
        } else {
            throw new Error(data.error || '获取机器人信息失败');
        }
    } catch (error) {
        console.error('❌ 获取Telegram机器人信息失败:', error);
        return null;
    }
}

// 发送消息到Telegram
async function sendTelegramMessage(text, parseMode = 'Markdown') {
    if (!telegramStatus.connected) {
        console.log('⚠️ Telegram机器人未连接，跳过消息发送');
        return false;
    }
    
    try {
        const response = await fetch('/api/telegram/sendMessage', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                text: text,
                parse_mode: parseMode
            })
        });
        
        const data = await response.json();
        
        if (data.success) {
            telegramStatus.lastMessage = text;
            telegramStatus.messageCount++;
            console.log('📤 Telegram消息发送成功');
            return true;
        } else {
            throw new Error(data.error || '发送消息失败');
        }
    } catch (error) {
        console.error('❌ 发送Telegram消息失败:', error);
        telegramStatus.errorCount++;
        return false;
    }
}

// 发送TG机器人连接通知
async function sendTelegramConnectionNotification(isReconnect = false) {
    const connectionType = isReconnect ? '重新连接' : '首次连接';
    const currentTime = new Date().toLocaleString('zh-CN');

    const message = `
🤖 *TG机器人${connectionType}成功*

⏰ 连接时间: ${currentTime}
🔗 连接状态: ✅ 已连接
📡 通信状态: 正常
🎯 功能状态: 全部激活

📋 *可用通知功能:*
• 🚀 系统启动/重启通知
• 💰 账户余额变动提醒
• 📈 交易执行结果推送
• ⚠️ 价格异常波动警报
• 🎯 支撑阻力位突破提醒
• 📊 每日交易总结报告
• 🔧 系统状态监控报告

🎉 TG机器人已准备就绪，开始监控交易系统...

*测试案例:*
• 当BTC价格突破$102,000时发送警报
• 当账户余额变动超过$10时通知
• 每日21:00发送交易总结报告
• 系统异常时立即发送警告
    `;

    await sendTelegramMessage(message);
}

// 发送系统启动消息
async function sendSystemStartMessage() {
    const message = MESSAGE_TEMPLATES.SYSTEM_START
        .replace('{timestamp}', new Date().toLocaleString('zh-CN'))
        .replace('{cryptoCount}', cryptos.length)
        .replace('{apiStatus}', apiStatus.connected ? '✅ 已连接' : '❌ 未连接');

    await sendTelegramMessage(message);
}

// 发送交易执行通知
async function sendTradeNotification(trade) {
    if (!telegramStatus.connected) return;
    
    const profitInfo = trade.profit >= 0 
        ? `📈 盈利: +${trade.profit.toFixed(2)} USDT`
        : `📉 亏损: ${trade.profit.toFixed(2)} USDT`;
    
    const message = MESSAGE_TEMPLATES.TRADE_EXECUTED
        .replace('{symbol}', trade.symbol)
        .replace('{type}', trade.type === 'buy' ? '买入' : '卖出')
        .replace('{amount}', trade.amount)
        .replace('{price}', trade.price)
        .replace('{totalValue}', (parseFloat(trade.amount) * trade.price).toFixed(2))
        .replace('{timestamp}', trade.time)
        .replace('{profitInfo}', profitInfo);
    
    await sendTelegramMessage(message);
}

// 发送账户余额报告
async function sendBalanceReport() {
    if (!telegramStatus.connected) return;
    
    const message = MESSAGE_TEMPLATES.BALANCE_REPORT
        .replace('{totalBalance}', accountBalance.totalBalance.toFixed(2))
        .replace('{availableBalance}', accountBalance.availableBalance.toFixed(2))
        .replace('{frozenBalance}', accountBalance.frozenBalance.toFixed(2))
        .replace('{dailyPnL}', accountBalance.dailyPnL.toFixed(2))
        .replace('{timestamp}', new Date().toLocaleString('zh-CN'));
    
    await sendTelegramMessage(message);
}

// 发送系统警告
async function sendSystemWarning(warningType, details) {
    if (!telegramStatus.connected) return;
    
    const message = MESSAGE_TEMPLATES.SYSTEM_WARNING
        .replace('{warningType}', warningType)
        .replace('{details}', details)
        .replace('{timestamp}', new Date().toLocaleString('zh-CN'));
    
    await sendTelegramMessage(message);
}

// 发送每日交易总结
async function sendDailySummary() {
    if (!telegramStatus.connected) return;
    
    const stats = window.getTradeStatistics();
    const bestTrade = window.tradeHistory.length > 0 
        ? window.tradeHistory.reduce((best, trade) => 
            trade.profit > best.profit ? trade : best
          )
        : null;
    
    const message = MESSAGE_TEMPLATES.DAILY_SUMMARY
        .replace('{date}', new Date().toLocaleDateString('zh-CN'))
        .replace('{tradeCount}', stats.totalTrades)
        .replace('{successRate}', stats.successRate)
        .replace('{totalPnL}', stats.totalProfit)
        .replace('{bestTrade}', bestTrade 
            ? `${bestTrade.symbol} +${bestTrade.profit.toFixed(2)} USDT`
            : '无'
        );
    
    await sendTelegramMessage(message);
}

// 发送价格警报
async function sendPriceAlert(symbol, price, changePercent) {
    if (!telegramStatus.connected) return;
    
    const alertType = changePercent > 0 ? '📈 涨幅警报' : '📉 跌幅警报';
    const emoji = changePercent > 0 ? '🚀' : '⚠️';
    
    const message = `
${emoji} *${alertType}*

💰 交易对: ${symbol}
💲 当前价格: ${formatPrice(price)} USDT
📊 24h变化: ${changePercent.toFixed(2)}%
⏰ 时间: ${new Date().toLocaleString('zh-CN')}

请关注市场动态！
    `;
    
    await sendTelegramMessage(message);
}

// 发送系统状态报告
async function sendSystemStatus() {
    if (!telegramStatus.connected) return;
    
    const uptime = Date.now() - (window.systemStartTime || Date.now());
    const uptimeHours = Math.floor(uptime / (1000 * 60 * 60));
    const uptimeMinutes = Math.floor((uptime % (1000 * 60 * 60)) / (1000 * 60));
    
    const message = `
📊 *系统状态报告*

🔗 API连接: ${apiStatus.connected ? '✅ 正常' : '❌ 异常'}
🤖 TG机器人: ${telegramStatus.connected ? '✅ 正常' : '❌ 异常'}
⏰ 运行时间: ${uptimeHours}小时${uptimeMinutes}分钟
📈 价格更新: ${apiStatus.websocketConnected ? '✅ 实时' : '❌ 断开'}
💰 账户余额: ${accountBalance.totalBalance.toFixed(2)} USDT
📊 交易次数: ${window.tradeHistory ? window.tradeHistory.length : 0}

系统运行正常 🚀
    `;
    
    await sendTelegramMessage(message);
}

// 定时发送报告
function startPeriodicReports() {
    // 每小时发送一次系统状态
    setInterval(async () => {
        await sendSystemStatus();
    }, 60 * 60 * 1000); // 1小时
    
    // 每天晚上10点发送交易总结
    const now = new Date();
    const tonight = new Date(now.getFullYear(), now.getMonth(), now.getDate(), 22, 0, 0);
    if (tonight <= now) {
        tonight.setDate(tonight.getDate() + 1);
    }
    
    const timeToTonight = tonight.getTime() - now.getTime();
    setTimeout(() => {
        sendDailySummary();
        // 然后每24小时发送一次
        setInterval(sendDailySummary, 24 * 60 * 60 * 1000);
    }, timeToTonight);
    
    console.log('📅 定时报告已启动');
}

// 监控价格异常波动
function monitorPriceAlerts() {
    const ALERT_THRESHOLD = 5; // 5%的价格变化触发警报
    
    setInterval(() => {
        cryptos.forEach(crypto => {
            if (Math.abs(crypto.change) >= ALERT_THRESHOLD) {
                sendPriceAlert(crypto.symbol, crypto.price, crypto.change);
            }
        });
    }, 5 * 60 * 1000); // 每5分钟检查一次
    
    console.log('📊 价格警报监控已启动');
}

// 导出函数到全局作用域
window.initializeTelegramBot = initializeTelegramBot;
window.sendTelegramMessage = sendTelegramMessage;
window.sendTradeNotification = sendTradeNotification;
window.sendBalanceReport = sendBalanceReport;
window.sendSystemWarning = sendSystemWarning;
window.sendDailySummary = sendDailySummary;
window.sendPriceAlert = sendPriceAlert;
window.sendSystemStatus = sendSystemStatus;
window.startPeriodicReports = startPeriodicReports;
window.monitorPriceAlerts = monitorPriceAlerts;
window.telegramStatus = telegramStatus;
window.restoreTelegramStatus = restoreTelegramStatus;
window.updateTelegramConnectionUI = updateTelegramConnectionUI;
window.saveTelegramStatus = saveTelegramStatus;
window.sendTelegramConnectionNotification = sendTelegramConnectionNotification;

// 页面加载时恢复TG状态 - 已移至main.js统一管理
// document.addEventListener('DOMContentLoaded', function() {
//     setTimeout(() => {
//         restoreTelegramStatus();
//     }, 1000);
// });

// ==================== 新增功能：智能技术分析 ====================

// 支撑阻力位自动检测算法
function detectSupportResistanceLevels(symbol, priceData, timeframe = '1h') {
    if (!priceData || priceData.length < 20) return null;

    const levels = {
        support: [],
        resistance: []
    };

    // 寻找局部高点和低点
    for (let i = 2; i < priceData.length - 2; i++) {
        const current = priceData[i];
        const prev2 = priceData[i-2];
        const prev1 = priceData[i-1];
        const next1 = priceData[i+1];
        const next2 = priceData[i+2];

        // 检测阻力位（局部高点）
        if (current.high > prev2.high && current.high > prev1.high &&
            current.high > next1.high && current.high > next2.high) {
            levels.resistance.push({
                price: current.high,
                timestamp: current.timestamp,
                touches: 1,
                volume: current.volume || 0
            });
        }

        // 检测支撑位（局部低点）
        if (current.low < prev2.low && current.low < prev1.low &&
            current.low < next1.low && current.low < next2.low) {
            levels.support.push({
                price: current.low,
                timestamp: current.timestamp,
                touches: 1,
                volume: current.volume || 0
            });
        }
    }

    // 合并相近的价位并计算强度
    levels.support = mergeSimilarLevels(levels.support);
    levels.resistance = mergeSimilarLevels(levels.resistance);

    // 按强度排序，取前2个
    levels.support.sort((a, b) => b.strength - a.strength).slice(0, 2);
    levels.resistance.sort((a, b) => b.strength - a.strength).slice(0, 2);

    // 缓存结果
    marketDataCache.supportLevels.set(symbol, levels.support);
    marketDataCache.resistanceLevels.set(symbol, levels.resistance);

    return levels;
}

// 合并相近的支撑阻力位
function mergeSimilarLevels(levels, threshold = 0.005) {
    const merged = [];

    levels.forEach(level => {
        const similar = merged.find(m =>
            Math.abs(m.price - level.price) / level.price < threshold
        );

        if (similar) {
            similar.touches++;
            similar.strength = similar.touches * (similar.volume + level.volume);
            similar.price = (similar.price + level.price) / 2;
        } else {
            level.strength = level.touches * level.volume;
            merged.push(level);
        }
    });

    return merged;
}

// 检查价格是否接近支撑阻力位
function checkPriceNearLevels(symbol, currentPrice) {
    const supportLevels = marketDataCache.supportLevels.get(symbol) || [];
    const resistanceLevels = marketDataCache.resistanceLevels.get(symbol) || [];

    const alerts = [];
    const threshold = 0.002; // 0.2% 接近阈值

    // 检查支撑位
    supportLevels.forEach((level, index) => {
        const distance = Math.abs(currentPrice - level.price) / level.price;
        if (distance < threshold) {
            alerts.push({
                type: 'support',
                level: index + 1,
                price: level.price,
                strength: level.strength,
                distance: distance
            });
        }
    });

    // 检查阻力位
    resistanceLevels.forEach((level, index) => {
        const distance = Math.abs(currentPrice - level.price) / level.price;
        if (distance < threshold) {
            alerts.push({
                type: 'resistance',
                level: index + 1,
                price: level.price,
                strength: level.strength,
                distance: distance
            });
        }
    });

    return alerts.length > 0 ? alerts : null;
}

// 发送支撑阻力位警报
async function sendSupportResistanceAlert(symbol, currentPrice, alerts) {
    if (!telegramStatus.connected || !alerts) return;

    const supportLevels = marketDataCache.supportLevels.get(symbol) || [];
    const resistanceLevels = marketDataCache.resistanceLevels.get(symbol) || [];

    const alert = alerts[0]; // 取最近的警报

    const message = MESSAGE_TEMPLATES.SUPPORT_RESISTANCE_ALERT
        .replace('{symbol}', symbol)
        .replace('{currentPrice}', formatPrice(currentPrice))
        .replace('{levelType}', alert.type === 'support' ? '支撑位' : '阻力位')
        .replace('{levelPrice}', formatPrice(alert.price))
        .replace('{support1}', supportLevels[0] ? formatPrice(supportLevels[0].price) : 'N/A')
        .replace('{support2}', supportLevels[1] ? formatPrice(supportLevels[1].price) : 'N/A')
        .replace('{resistance1}', resistanceLevels[0] ? formatPrice(resistanceLevels[0].price) : 'N/A')
        .replace('{resistance2}', resistanceLevels[1] ? formatPrice(resistanceLevels[1].price) : 'N/A')
        .replace('{suggestion}', getSuggestionForLevel(alert))
        .replace('{strength}', Math.min(10, Math.floor(alert.strength / 1000)))
        .replace('{timeframe}', '1小时')
        .replace('{timestamp}', new Date().toLocaleString('zh-CN'));

    await sendTelegramMessage(message);
}

// 获取技术分析建议
function getSuggestionForLevel(alert) {
    if (alert.type === 'support') {
        return alert.level === 1 ? '关注反弹机会' : '强支撑位，考虑买入';
    } else {
        return alert.level === 1 ? '关注回调风险' : '强阻力位，考虑减仓';
    }
}

// 扩展导出函数
window.detectSupportResistanceLevels = detectSupportResistanceLevels;
window.checkPriceNearLevels = checkPriceNearLevels;
window.sendSupportResistanceAlert = sendSupportResistanceAlert;
window.marketDataCache = marketDataCache;

console.log('🤖 Telegram机器人集成模块已加载');
