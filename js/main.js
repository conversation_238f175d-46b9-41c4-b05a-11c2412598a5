// 断金ใ现货'杠杆自动化交易系统 - 主程序入口
// 功能：系统初始化、定时更新、事件绑定等核心功能

// 系统配置参数
const CONFIG = {
    UPDATE_INTERVAL: 3000,    // 价格更新间隔（毫秒）
    BACKUP_INTERVAL: 600000,  // 备份间隔（毫秒）- 10分钟
    MAX_TRADE_RECORDS: 10,    // 最大交易记录数
    AUTO_TRADE_ENABLED: true  // 自动交易开关
};

// 系统状态
let systemStatus = {
    isRunning: false,
    autoTradeEnabled: CONFIG.AUTO_TRADE_ENABLED,
    lastUpdate: null,
    updateCount: 0
};

// API连接状态
let apiStatus = {
    binance: {
        connected: false,
        lastCheck: null,
        retryCount: 0
    },
    telegram: {
        connected: false,
        lastCheck: null,
        retryCount: 0
    }
};

// 定时器引用
let priceUpdateTimer = null;
let backupTimer = null;

// 系统初始化函数
async function initializeSystem() {
    console.log('🚀 断金交易系统启动中...');

    try {
        // 记录系统启动时间
        window.systemStartTime = Date.now();

        // 初始化UI组件
        initializeUI();

        // 初始化时显示未登录状态 - 确保函数已加载
        if (typeof updateBalanceDisplay === 'function') {
            updateBalanceDisplay();
        } else {
            console.warn('⚠️ updateBalanceDisplay函数尚未加载，将延迟调用');
            setTimeout(() => {
                if (typeof updateBalanceDisplay === 'function') {
                    updateBalanceDisplay();
                } else {
                    console.error('❌ updateBalanceDisplay函数加载失败');
                }
            }, 500);
        }

        // 启动矩阵背景动画
        initializeMatrix();

        // 绑定事件监听器
        bindEventListeners();

        // 初始化模态框表单
        initializeModalForms();

        // 初始化实盘数据（币安API + Telegram）
        console.log('🔄 初始化实盘数据连接...');
        if (window.initializeRealData) {
            await window.initializeRealData();
        }

        // 启动价格更新定时器
        startPriceUpdates();

        // 启动自动备份
        startAutoBackup();

        // 启动API连接状态检查
        startApiConnectionCheck();

        // 初始化API连接状态显示
        updateConnectionStatus('binance', window.apiStatus?.connected || false);
        updateConnectionStatus('telegram', window.telegramStatus?.connected || false);

        // 如果API已连接，重新获取账户余额
        if (window.apiStatus?.connected) {
            console.log('🔄 API已连接，重新获取账户余额...');
            setTimeout(async () => {
                await refreshAccountBalances();
                // 确保现货账户标签页是激活状态
                switchAccountType('spot');
            }, 3000);
        } else {
            // 即使API未连接，也要确保UI状态正确
            setTimeout(() => {
                switchAccountType('spot');
            }, 1000);
        }

        // 启动实盘数据定期更新
        if (window.startRealDataUpdates) {
            window.startRealDataUpdates();
        }

        // 加载实盘交易记录
        console.log('📊 加载实盘交易记录...');
        setTimeout(async () => {
            await initializePagination();
        }, 2000); // 延迟2秒，确保API连接稳定

        // 启动Telegram定时报告
        if (window.startPeriodicReports) {
            window.startPeriodicReports();
        }

        // 启动价格警报监控
        if (window.monitorPriceAlerts) {
            window.monitorPriceAlerts();
        }

        // 初始化K线图模块 - 强制使用实盘数据
        console.log('📈 初始化K线图模块...');
        setTimeout(async () => {
            console.log('🔍 检查K线图容器...');
            const container = document.getElementById('klineChartContainer');
            if (!container) {
                console.error('❌ K线图容器未找到');
                return;
            }

            console.log('📊 直接加载实盘K线数据...');
            await loadRealKlineData('BTCUSDT', '1h');

            // 绑定K线图控制事件
            bindKlineEvents();

            // 启动实时更新
            startKlineRealTimeUpdates();

        }, 3000); // 延迟3秒确保所有模块加载完成

        // 绑定K线图控制事件
        bindKlineEvents();

        // 测试TG机器人通知 - 发送系统启动消息
        if (window.telegramStatus?.connected) {
            console.log('🤖 发送TG系统启动通知...');
            testTelegramNotifications();
        }

        // 更新系统状态
        systemStatus.isRunning = true;
        systemStatus.lastUpdate = new Date();

        console.log('✅ 系统启动成功！');
        console.log('📊 支持19种加密货币实盘交易');
        console.log('📈 专业K线图表集成完成');
        console.log('🎯 支撑阻力位自动检测');
        console.log('🔗 币安API集成完成');
        console.log('🤖 Telegram机器人集成完成');
        console.log('📱 完全响应式设计');
        console.log('💾 自动备份已启用');

        // 显示欢迎消息
        showWelcomeMessage();

    } catch (error) {
        console.error('❌ 系统启动失败:', error);
        showErrorMessage('系统启动失败，请刷新页面重试');
    }
}

// 启动价格更新定时器
function startPriceUpdates() {
    if (priceUpdateTimer) {
        clearInterval(priceUpdateTimer);
    }
    
    priceUpdateTimer = setInterval(() => {
        try {
            // 更新加密货币价格
            updateCryptoPrices();
            
            // 更新UI显示
            updateCryptoGrid();
            if (typeof updateBalanceDisplay === 'function') {
                updateBalanceDisplay();
            }
            
            // 更新系统状态
            systemStatus.lastUpdate = new Date();
            systemStatus.updateCount++;
            
            console.log(`📈 价格更新 #${systemStatus.updateCount}`);
            
        } catch (error) {
            console.error('❌ 价格更新失败:', error);
        }
    }, CONFIG.UPDATE_INTERVAL);
    
    console.log(`⏰ 价格更新定时器已启动 (${CONFIG.UPDATE_INTERVAL/1000}秒间隔)`);
}

// 启动自动备份
function startAutoBackup() {
    if (backupTimer) {
        clearInterval(backupTimer);
    }
    
    backupTimer = setInterval(() => {
        try {
            createSystemBackup();
            console.log('💾 自动备份完成');
        } catch (error) {
            console.error('❌ 自动备份失败:', error);
        }
    }, CONFIG.BACKUP_INTERVAL);
    
    console.log(`💾 自动备份已启动 (${CONFIG.BACKUP_INTERVAL/60000}分钟间隔)`);
}

// 启动API连接状态检查
function startApiConnectionCheck() {
    // 每10秒检查一次API连接状态
    setInterval(() => {
        checkApiConnections();
    }, 10000);

    console.log('🔗 API连接状态检查已启动');
}

// 绑定事件监听器
function bindEventListeners() {
    // 紧急停止按钮
    const emergencyBtn = document.querySelector('.emergency-btn');
    if (emergencyBtn) {
        emergencyBtn.addEventListener('click', handleEmergencyStop);
    }
    
    // 自动交易开关
    const autoTradeToggle = document.getElementById('autoTradeToggle');
    if (autoTradeToggle) {
        autoTradeToggle.addEventListener('click', toggleAutoTrade);
    }

    // API登录按钮
    const binanceLoginBtn = document.getElementById('binanceLoginBtn');
    const telegramLoginBtn = document.getElementById('telegramLoginBtn');

    if (binanceLoginBtn) {
        binanceLoginBtn.addEventListener('click', handleBinanceLogin);
    }

    if (telegramLoginBtn) {
        telegramLoginBtn.addEventListener('click', handleTelegramLogin);
    }
    
    // 平台切换按钮
    const platformBtns = document.querySelectorAll('.platform-btn');
    platformBtns.forEach(btn => {
        btn.addEventListener('click', handlePlatformSwitch);
    });
    
    // 窗口大小变化事件
    window.addEventListener('resize', handleWindowResize);
    
    // 页面可见性变化事件
    document.addEventListener('visibilitychange', handleVisibilityChange);

    // 策略选择监听器
    const buyStrategySelect = document.getElementById('buyStrategy');
    const sellStrategySelect = document.getElementById('sellStrategy');
    const timeFrameSelect = document.getElementById('timeFrame');

    if (buyStrategySelect) {
        buyStrategySelect.addEventListener('change', function() {
            console.log(`📈 买入策略变更为: ${this.value}`);
            if (window.updateTradingStrategies) {
                window.updateTradingStrategies();
            }
        });
    }

    if (sellStrategySelect) {
        sellStrategySelect.addEventListener('change', function() {
            console.log(`📉 卖出策略变更为: ${this.value}`);
            if (window.updateTradingStrategies) {
                window.updateTradingStrategies();
            }
        });
    }

    if (timeFrameSelect) {
        timeFrameSelect.addEventListener('change', function() {
            console.log(`⏰ 交易周期变更为: ${this.value}`);
            if (window.updateTradingStrategies) {
                window.updateTradingStrategies();
            }
        });
    }

    // 杠杆按钮事件监听器 - 使用事件委托
    document.addEventListener('click', function(event) {
        if (event.target.classList.contains('leverage-btn-small')) {
            handleLeverageClick(event.target);
        }
    });

    console.log('🔗 事件监听器绑定完成');
}

// 紧急停止处理
function handleEmergencyStop() {
    console.log('🚨 紧急停止触发！');
    
    // 停止所有定时器
    if (priceUpdateTimer) {
        clearInterval(priceUpdateTimer);
        priceUpdateTimer = null;
    }
    
    if (backupTimer) {
        clearInterval(backupTimer);
        backupTimer = null;
    }
    
    // 禁用自动交易
    systemStatus.autoTradeEnabled = false;
    systemStatus.isRunning = false;
    
    // 更新UI状态
    const autoTradeToggle = document.getElementById('autoTradeToggle');
    if (autoTradeToggle) {
        autoTradeToggle.className = 'toggle-btn inactive';
        autoTradeToggle.textContent = '已停止';
    }
    
    // 显示停止消息
    showSystemMessage('🚨 系统已紧急停止！所有自动交易已暂停。', 'error');
    
    // 创建紧急备份
    createSystemBackup();
}

// 自动交易开关
function toggleAutoTrade() {
    systemStatus.autoTradeEnabled = !systemStatus.autoTradeEnabled;
    
    const autoTradeToggle = document.getElementById('autoTradeToggle');
    if (autoTradeToggle) {
        if (systemStatus.autoTradeEnabled) {
            autoTradeToggle.className = 'toggle-btn active';
            autoTradeToggle.textContent = '已启用';
            showSystemMessage('✅ 自动交易已启用', 'success');
        } else {
            autoTradeToggle.className = 'toggle-btn inactive';
            autoTradeToggle.textContent = '已禁用';
            showSystemMessage('⏸️ 自动交易已禁用', 'warning');
        }
    }
    
    console.log(`🔄 自动交易: ${systemStatus.autoTradeEnabled ? '启用' : '禁用'}`);
}

// 平台切换处理
function handlePlatformSwitch(event) {
    const button = event.target;
    const platform = button.textContent;
    
    // 移除其他按钮的激活状态
    document.querySelectorAll('.platform-btn').forEach(btn => {
        btn.classList.remove('active');
    });
    
    // 激活当前按钮
    button.classList.add('active');
    
    console.log(`🔄 切换到平台: ${platform}`);
    showSystemMessage(`已切换到 ${platform}`, 'info');
}

// 窗口大小变化处理
function handleWindowResize() {
    // 重新计算布局
    updateResponsiveLayout();
    
    // 重新初始化矩阵动画
    if (window.resizeMatrix) {
        window.resizeMatrix();
    }
}

// 页面可见性变化处理
function handleVisibilityChange() {
    if (document.hidden) {
        // 页面隐藏时减少更新频率
        console.log('📱 页面隐藏，降低更新频率');
    } else {
        // 页面显示时恢复正常更新
        console.log('📱 页面显示，恢复正常更新');
        if (systemStatus.isRunning && !priceUpdateTimer) {
            startPriceUpdates();
        }
    }
}

// 创建系统备份
function createSystemBackup() {
    const backupData = {
        timestamp: new Date().toISOString(),
        systemStatus: systemStatus,
        tradeHistory: window.tradeHistory || [],
        cryptoData: window.cryptos || [],
        positions: window.positions || []
    };
    
    // 保存到本地存储
    localStorage.setItem('tradingSystemBackup', JSON.stringify(backupData));
    
    console.log('💾 系统备份已保存到本地存储');
}

// 恢复系统备份
function restoreSystemBackup() {
    try {
        const backupData = localStorage.getItem('tradingSystemBackup');
        if (backupData) {
            const data = JSON.parse(backupData);
            
            // 恢复交易记录
            if (data.tradeHistory && window.tradeHistory) {
                window.tradeHistory.length = 0;
                window.tradeHistory.push(...data.tradeHistory);
            }
            
            console.log('📥 系统备份已恢复');
            return true;
        }
    } catch (error) {
        console.error('❌ 备份恢复失败:', error);
    }
    return false;
}

// 显示欢迎消息
function showWelcomeMessage() {
    const messages = [
        '🎉 欢迎使用断金交易系统！',
        '💰 支持21种主流加密货币',
        '📱 完全响应式设计',
        '🛡️ 内置风险控制系统',
        '💾 自动备份保护数据'
    ];
    
    messages.forEach((message, index) => {
        setTimeout(() => {
            console.log(message);
        }, index * 500);
    });
}

// 显示系统消息
function showSystemMessage(message, type = 'info') {
    console.log(`${getMessageIcon(type)} ${message}`);
    
    // 可以在这里添加UI通知显示
    // 例如：显示toast通知或状态栏消息
}

// 获取消息图标
function getMessageIcon(type) {
    const icons = {
        success: '✅',
        error: '❌',
        warning: '⚠️',
        info: 'ℹ️'
    };
    return icons[type] || 'ℹ️';
}

// 显示错误消息
function showErrorMessage(message) {
    console.error(`❌ ${message}`);
    showSystemMessage(message, 'error');
}

// 系统健康检查
function healthCheck() {
    const health = {
        systemRunning: systemStatus.isRunning,
        autoTradeEnabled: systemStatus.autoTradeEnabled,
        lastUpdate: systemStatus.lastUpdate,
        updateCount: systemStatus.updateCount,
        timersActive: {
            priceUpdate: !!priceUpdateTimer,
            backup: !!backupTimer
        }
    };
    
    console.log('🏥 系统健康检查:', health);
    return health;
}

// 获取系统信息
function getSystemInfo() {
    return {
        version: '1.0.0',
        status: systemStatus,
        config: CONFIG,
        cryptoCount: window.cryptos ? window.cryptos.length : 0,
        tradeCount: window.tradeHistory ? window.tradeHistory.length : 0
    };
}

// 重启系统
function restartSystem() {
    console.log('🔄 重启系统...');
    
    // 停止所有定时器
    if (priceUpdateTimer) clearInterval(priceUpdateTimer);
    if (backupTimer) clearInterval(backupTimer);
    
    // 重新初始化
    setTimeout(() => {
        initializeSystem();
    }, 1000);
}

// API连接处理函数
async function toggleBinanceConnection() {
    if (window.apiStatus && window.apiStatus.connected) {
        // 如果已连接，则断开连接
        window.apiStatus.connected = false;
        window.accountBalance = null;
        updateConnectionStatus('binance', false);
        console.log('🔌 币安API已断开连接');
        return;
    }

    // 如果未连接，则显示登录界面
    showBinanceModal();
}

async function handleBinanceLogin() {
    console.log('🔑 尝试连接币安API...');

    // 显示币安API配置模态框
    showBinanceModal();
}



function toggleTelegramConnection() {
    // 检查TG机器人连接状态
    const isConnected = window.telegramStatus?.connected || false;

    if (isConnected) {
        // 如果已连接，则断开连接
        if (window.telegramStatus) {
            window.telegramStatus.connected = false;
        }
        localStorage.removeItem('telegramStatus');
        updateConnectionStatus('telegram', false);
        console.log('🔌 TG机器人已断开连接');
        return;
    }

    // 如果未连接，则显示登录界面
    showTelegramModal();
}

// 显示币安登录模态框
function showBinanceModal() {
    const modal = document.getElementById('binanceLoginModal');
    if (modal) {
        modal.style.display = 'block';

        // 预填充已保存的API信息（如果有的话）
        const savedApiKey = localStorage.getItem('binance_api_key');
        const savedTestnet = localStorage.getItem('binance_use_testnet');

        if (savedApiKey && savedApiKey !== 'undefined' && savedApiKey.length > 10) {
            document.getElementById('binanceApiKey').value = savedApiKey;
            console.log('🔑 已预填充API Key:', savedApiKey.substring(0, 8) + '...');
        }

        if (savedTestnet === 'true') {
            document.getElementById('binanceTestnet').checked = true;
        }
    }
}

// 关闭币安登录模态框
function closeBinanceModal() {
    const modal = document.getElementById('binanceLoginModal');
    if (modal) {
        modal.style.display = 'none';
        // 清空表单
        document.getElementById('binanceLoginForm').reset();
    }
}

// 清除保存的API配置（调试用）
function clearSavedApiConfig() {
    localStorage.removeItem('binance_api_key');
    localStorage.removeItem('binance_use_testnet');
    localStorage.removeItem('telegram_bot_token');
    localStorage.removeItem('telegram_chat_id');
    console.log('🗑️ 已清除所有保存的API配置');
}

// 显示Telegram登录模态框
function showTelegramModal() {
    const modal = document.getElementById('telegramLoginModal');
    if (modal) {
        modal.style.display = 'block';

        // 预填充已保存的配置信息（如果有的话）
        const savedBotToken = localStorage.getItem('telegram_bot_token');
        const savedChatId = localStorage.getItem('telegram_chat_id');
        if (savedBotToken) {
            document.getElementById('telegramBotToken').value = savedBotToken;
        }
        if (savedChatId) {
            document.getElementById('telegramChatId').value = savedChatId;
        }
    }
}

// 关闭Telegram登录模态框
function closeTelegramModal() {
    const modal = document.getElementById('telegramLoginModal');
    if (modal) {
        modal.style.display = 'none';
        // 清空表单
        document.getElementById('telegramLoginForm').reset();
    }
}

function handleTelegramLogin() {
    console.log('🤖 尝试连接TG机器人...');
    const btn = document.getElementById('telegramLoginBtn');
    const statusText = document.getElementById('telegramStatusText');

    // 模拟连接过程
    if (statusText) statusText.textContent = '连接中...';
    btn.disabled = true;

    // 模拟TG机器人连接
    setTimeout(() => {
        // 模拟连接成功
        apiStatus.telegram.connected = true;
        apiStatus.telegram.lastCheck = new Date();
        apiStatus.telegram.retryCount = 0;

        updateConnectionStatus('telegram', true);
        btn.disabled = false;

        console.log('✅ TG机器人连接成功');
    }, 1500);
}

// 更新连接状态显示
function updateConnectionStatus(service, isConnected) {
    // 更新登录按钮状态
    const loginBtn = document.getElementById(`${service}LoginBtn`);
    const statusLight = document.getElementById(`${service}StatusLight`);
    const statusText = document.getElementById(`${service}StatusText`);

    if (loginBtn && statusLight && statusText) {
        if (isConnected) {
            loginBtn.classList.add('connected');
            statusLight.className = 'status-light connected';
            statusText.textContent = '已连接';
        } else {
            loginBtn.classList.remove('connected');
            statusLight.className = 'status-light disconnected';
            statusText.textContent = '登录';
        }
    }

    // 兼容右侧面板的状态显示（如果存在）
    const statusElement = document.getElementById(`${service}Status`);
    if (statusElement) {
        const lightElement = statusElement.querySelector('.status-light');
        const textElement = statusElement.querySelector('.status-text');

        if (lightElement && textElement) {
            if (isConnected) {
                lightElement.className = 'status-light green';
                textElement.textContent = '已连接';
            } else {
                lightElement.className = 'status-light red';
                textElement.textContent = '未连接';
            }
        }
    }
}

// 定期检查API连接状态
function checkApiConnections() {
    // 检查币安API连接
    if (apiStatus.binance.connected) {
        // 模拟连接检查（实际项目中会ping API）
        const timeSinceLastCheck = Date.now() - apiStatus.binance.lastCheck;
        if (timeSinceLastCheck > 30000) { // 30秒无响应视为断线
            apiStatus.binance.connected = false;
            updateConnectionStatus('binance', false);
            console.log('⚠️ 币安API连接超时');
        }
    }

    // 检查TG机器人连接
    if (apiStatus.telegram.connected) {
        const timeSinceLastCheck = Date.now() - apiStatus.telegram.lastCheck;
        if (timeSinceLastCheck > 30000) {
            apiStatus.telegram.connected = false;
            updateConnectionStatus('telegram', false);
            console.log('⚠️ TG机器人连接超时');
        }
    }
}

// 导出函数到全局作用域
window.tradingSystem = {
    healthCheck,
    getSystemInfo,
    restart: restartSystem,
    emergencyStop: handleEmergencyStop,
    toggleAutoTrade,
    createBackup: createSystemBackup,
    restoreBackup: restoreSystemBackup,
    binanceLogin: handleBinanceLogin,
    telegramLogin: handleTelegramLogin,
    updateConnectionStatus,
    checkApiConnections
};

// 页面加载完成后初始化系统
document.addEventListener('DOMContentLoaded', initializeSystem);

// 页面卸载前保存数据
window.addEventListener('beforeunload', () => {
    createSystemBackup();
    console.log('💾 页面卸载前备份完成');
});

// 杠杆按钮点击处理 - 币安支持的杠杆倍数
function handleLeverageClick(button) {
    const currentText = button.textContent;
    const leverageOptions = ['1x', '3x']; // 币安支持的杠杆倍数
    const currentIndex = leverageOptions.indexOf(currentText);
    const nextIndex = (currentIndex + 1) % leverageOptions.length;
    const nextLeverage = leverageOptions[nextIndex];

    button.textContent = nextLeverage;

    // 根据杠杆倍数改变按钮颜色
    button.style.background = nextLeverage === '1x' ? 'rgba(0, 255, 136, 0.2)' :
                             'rgba(255, 152, 0, 0.2)'; // 3x橙色

    button.style.borderColor = nextLeverage === '1x' ? '#00ff88' :
                              '#ff9800'; // 3x橙色边框

    button.style.color = nextLeverage === '1x' ? '#00ff88' :
                        '#ff9800'; // 3x橙色文字

    console.log(`⚡ 杠杆设置变更为: ${nextLeverage}`);

    // 显示系统消息
    if (window.showSystemMessage) {
        window.showSystemMessage(`杠杆设置已变更为 ${nextLeverage}`, 'info');
    }
}

// 分页功能 - 交易记录分页管理
let currentPage = 1;
const recordsPerPage = 8; // 每页显示8条记录
let totalRecords = 0;
let allTradeRecords = []; // 存储所有交易记录
let spotTradeRecords = []; // 现货交易记录
let marginTradeRecords = []; // 杠杆交易记录
let currentTradeType = 'spot'; // 当前显示的交易类型

// 切换页面
function changePage(direction) {
    const totalPages = Math.ceil(totalRecords / recordsPerPage);

    if (direction === 'prev' && currentPage > 1) {
        currentPage--;
    } else if (direction === 'next' && currentPage < totalPages) {
        currentPage++;
    }

    updateTradeHistoryDisplay();
    updatePaginationControls();
}

// 跳转到指定页面
function goToPage(page) {
    const totalPages = Math.ceil(totalRecords / recordsPerPage);

    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        updateTradeHistoryDisplay();
        updatePaginationControls();
    }
}

// 更新分页控制按钮状态
function updatePaginationControls() {
    const totalPages = Math.ceil(totalRecords / recordsPerPage);
    const prevBtn = document.getElementById('prevBtn');
    const nextBtn = document.getElementById('nextBtn');
    const pageNumbers = document.getElementById('pageNumbers');

    // 更新前后按钮状态
    if (prevBtn) {
        prevBtn.disabled = currentPage === 1;
    }
    if (nextBtn) {
        nextBtn.disabled = currentPage === totalPages || totalPages === 0;
    }

    // 更新页码按钮
    if (pageNumbers) {
        pageNumbers.innerHTML = '';

        // 计算显示的页码范围
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);

        // 调整起始页
        if (endPage - startPage < 4) {
            startPage = Math.max(1, endPage - 4);
        }

        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `page-btn ${i === currentPage ? 'active' : ''}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => goToPage(i);
            pageNumbers.appendChild(pageBtn);
        }
    }
}

// 更新交易记录显示
function updateTradeHistoryDisplay() {
    const startIndex = (currentPage - 1) * recordsPerPage;
    const endIndex = startIndex + recordsPerPage;
    const pageRecords = allTradeRecords.slice(startIndex, endIndex);

    const tradeTable = document.getElementById('tradeTable');
    if (tradeTable) {
        if (pageRecords.length === 0) {
            tradeTable.innerHTML = `
                <tr>
                    <td colspan="7" style="text-align: center; color: rgba(255,255,255,0.5); padding: 20px;">
                        暂无交易记录
                    </td>
                </tr>
            `;
        } else {
            tradeTable.innerHTML = pageRecords.map(record => `
                <tr>
                    <td class="table-cell">${record.time}</td>
                    <td class="table-cell">${record.symbol}</td>
                    <td class="table-cell">${record.type}</td>
                    <td class="table-cell">${record.amount}</td>
                    <td class="table-cell">${record.price}</td>
                    <td class="table-cell ${record.profit >= 0 ? 'price-up' : 'price-down'}">${record.profit}</td>
                    <td class="table-cell">${record.status}</td>
                </tr>
            `).join('');
        }
    }
}

// 添加交易记录（供其他模块调用）
function addTradeRecord(record) {
    allTradeRecords.unshift(record); // 新记录添加到开头
    totalRecords = allTradeRecords.length;

    // 如果当前在第一页，更新显示
    if (currentPage === 1) {
        updateTradeHistoryDisplay();
    }

    updatePaginationControls();
}

// 初始化交易记录 - 接入币安实盘数据
async function initializePagination() {
    console.log('📊 初始化实盘交易记录...');

    try {
        // 获取币安实盘交易记录
        await loadRealTradeHistory();

        // 如果没有实盘数据，显示空状态
        if (!allTradeRecords || allTradeRecords.length === 0) {
            allTradeRecords = [];
            totalRecords = 0;
            currentPage = 1;

            console.log('📝 暂无交易记录，显示空状态');
        }

        updateTradeHistoryDisplay();
        updatePaginationControls();

    } catch (error) {
        console.error('❌ 交易记录初始化失败:', error);

        // 错误时显示空状态
        allTradeRecords = [];
        totalRecords = 0;
        currentPage = 1;

        updateTradeHistoryDisplay();
        updatePaginationControls();
    }
}

// 加载币安实盘交易历史
async function loadRealTradeHistory() {
    try {
        console.log('🔄 获取币安实盘交易记录...');

        if (!window.apiStatus?.connected) {
            console.log('⚠️ 币安API未连接，无法获取交易记录');
            return;
        }

        const tradeRecords = [];

        // 获取主要交易对的交易记录
        const symbols = ['BTCUSDT', 'ETHUSDT', 'BNBUSDT', 'ADAUSDT', 'SOLUSDT'];

        for (const symbol of symbols) {
            try {
                console.log(`📈 获取 ${symbol} 交易记录...`);

                const response = await fetch(`/api/binance/myTrades?symbol=${symbol}&limit=10`);
                const data = await response.json();

                if (data.success && data.data && data.data.length > 0) {
                    console.log(`✅ ${symbol} 获取到 ${data.data.length} 条交易记录`);

                    // 转换币安交易数据格式
                    const formattedTrades = data.data.map(trade => {
                        const tradeTime = new Date(trade.time);
                        const isBuyer = trade.isBuyer;
                        const accountType = trade.marginAsset ? '杠杆' : '现货'; // 判断账户类型

                        return {
                            time: tradeTime.toLocaleTimeString('zh-CN'),
                            symbol: symbol.replace('USDT', '/USDT'),
                            type: `${accountType} ${isBuyer ? '买入' : '卖出'}`,
                            amount: parseFloat(trade.qty).toFixed(6),
                            price: `$${formatPrice(parseFloat(trade.price))}`,
                            profit: calculateTradeProfit(trade),
                            status: '已完成',
                            rawData: trade // 保存原始数据
                        };
                    });

                    tradeRecords.push(...formattedTrades);
                } else {
                    console.log(`📝 ${symbol} 暂无交易记录`);
                }

                // 避免API限制，添加延迟
                await new Promise(resolve => setTimeout(resolve, 200));

            } catch (error) {
                console.error(`❌ 获取 ${symbol} 交易记录失败:`, error);
            }
        }

        // 按时间排序（最新的在前）
        tradeRecords.sort((a, b) => {
            const timeA = a.rawData ? a.rawData.time : 0;
            const timeB = b.rawData ? b.rawData.time : 0;
            return timeB - timeA;
        });

        // 分离现货和杠杆交易记录
        spotTradeRecords = tradeRecords.filter(trade => !trade.type.includes('杠杆'));
        marginTradeRecords = tradeRecords.filter(trade => trade.type.includes('杠杆'));

        // 根据当前选择的类型设置显示数据
        if (currentTradeType === 'spot') {
            allTradeRecords = spotTradeRecords;
        } else {
            allTradeRecords = marginTradeRecords;
        }

        totalRecords = allTradeRecords.length;
        currentPage = 1;

        console.log(`✅ 实盘交易记录加载完成: ${totalRecords} 条记录`);

        // 发送TG通知
        if (totalRecords > 0 && window.sendTelegramMessage) {
            const message = `
📊 *交易记录同步完成*

📈 总交易数: ${totalRecords} 笔
🔄 数据来源: 币安实盘API
⏰ 同步时间: ${new Date().toLocaleString('zh-CN')}

最新交易:
${tradeRecords.slice(0, 3).map(trade =>
    `• ${trade.time} ${trade.symbol} ${trade.type} ${trade.amount}`
).join('\n')}

💰 实盘数据已同步到交易系统
            `;

            await window.sendTelegramMessage(message);
        }

    } catch (error) {
        console.error('❌ 加载实盘交易记录失败:', error);
        throw error;
    }
}

// 计算交易盈亏
function calculateTradeProfit(trade) {
    // 这里可以根据实际需求计算盈亏
    // 暂时返回手续费信息
    const commission = parseFloat(trade.commission || 0);
    const commissionAsset = trade.commissionAsset || 'USDT';

    if (commission > 0) {
        return `-$${commission.toFixed(4)} (${commissionAsset})`;
    }

    return '--';
}

// 绑定K线图控制事件
function bindKlineEvents() {
    // 交易对选择
    const symbolSelect = document.getElementById('klineSymbolSelect');
    if (symbolSelect) {
        symbolSelect.addEventListener('change', async function() {
            const symbol = this.value;
            console.log(`📊 切换交易对: ${symbol}`);

            // 显示加载状态
            showKlineLoading(true);

            if (window.switchSymbol) {
                await window.switchSymbol(symbol);
            }

            // 隐藏加载状态
            showKlineLoading(false);

            // 更新技术指标显示
            updateTechnicalIndicators(symbol);
        });
    }

    // 时间框架选择
    const timeframeSelect = document.getElementById('klineTimeframeSelect');
    if (timeframeSelect) {
        timeframeSelect.addEventListener('change', async function() {
            const timeframe = this.value;
            console.log(`⏰ 切换时间框架: ${timeframe}`);

            // 显示加载状态
            showKlineLoading(true);

            if (window.switchTimeframe) {
                await window.switchTimeframe(timeframe);
            }

            // 隐藏加载状态
            showKlineLoading(false);

            // 更新技术指标显示
            updateTechnicalIndicators(symbolSelect?.value || 'BTCUSDT');
        });
    }

    // 刷新按钮
    const refreshBtn = document.getElementById('klineRefreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', async function() {
            console.log('🔄 手动刷新K线数据');

            // 显示加载状态
            showKlineLoading(true);

            const symbol = symbolSelect?.value || 'BTCUSDT';
            const timeframe = timeframeSelect?.value || '1h';

            if (window.switchSymbol) {
                await window.switchSymbol(symbol);
            }

            // 隐藏加载状态
            showKlineLoading(false);

            // 更新技术指标显示
            updateTechnicalIndicators(symbol);
        });
    }

    // 全屏按钮
    const fullscreenBtn = document.getElementById('klineFullscreenBtn');
    if (fullscreenBtn) {
        fullscreenBtn.addEventListener('click', function() {
            toggleKlineFullscreen();
        });
    }
}

// 显示/隐藏K线图加载状态
function showKlineLoading(show) {
    const loadingElement = document.getElementById('klineLoading');
    if (loadingElement) {
        loadingElement.style.display = show ? 'block' : 'none';
    }
}

// 更新技术指标显示
function updateTechnicalIndicators(symbol) {
    // 获取支撑阻力位数据
    if (window.marketDataCache) {
        const supportLevels = window.marketDataCache.supportLevels?.get(symbol) || [];
        const resistanceLevels = window.marketDataCache.resistanceLevels?.get(symbol) || [];

        // 更新支撑位显示
        const supportElement = document.getElementById('supportLevels');
        if (supportElement) {
            if (supportLevels.length > 0) {
                const supportText = supportLevels.map((level, index) =>
                    `${formatPrice(level.price)}`
                ).join(' / ');
                supportElement.textContent = supportText;
            } else {
                supportElement.textContent = '检测中...';
            }
        }

        // 更新阻力位显示
        const resistanceElement = document.getElementById('resistanceLevels');
        if (resistanceElement) {
            if (resistanceLevels.length > 0) {
                const resistanceText = resistanceLevels.map((level, index) =>
                    `${formatPrice(level.price)}`
                ).join(' / ');
                resistanceElement.textContent = resistanceText;
            } else {
                resistanceElement.textContent = '检测中...';
            }
        }

        // 更新交易建议
        const suggestionElement = document.getElementById('tradingSuggestion');
        if (suggestionElement) {
            const suggestion = generateTradingSuggestion(symbol, supportLevels, resistanceLevels);
            suggestionElement.textContent = suggestion;
        }
    }
}

// 生成交易建议
function generateTradingSuggestion(symbol, supportLevels, resistanceLevels) {
    const crypto = cryptos.find(c => c.symbol === symbol);
    if (!crypto || !crypto.price) return '数据不足';

    const currentPrice = crypto.price;

    // 检查价格相对于支撑阻力位的位置
    if (supportLevels.length > 0 && resistanceLevels.length > 0) {
        const nearestSupport = supportLevels[0];
        const nearestResistance = resistanceLevels[0];

        const supportDistance = Math.abs(currentPrice - nearestSupport.price) / currentPrice;
        const resistanceDistance = Math.abs(currentPrice - nearestResistance.price) / currentPrice;

        if (supportDistance < 0.01) {
            return '接近支撑位，关注反弹';
        } else if (resistanceDistance < 0.01) {
            return '接近阻力位，注意回调';
        } else if (currentPrice > nearestSupport.price && currentPrice < nearestResistance.price) {
            return '震荡区间，等待突破';
        }
    }

    return '持续观察';
}

// 切换K线图全屏模式
function toggleKlineFullscreen() {
    const klinePanel = document.querySelector('.kline-panel');
    if (!klinePanel) return;

    if (klinePanel.classList.contains('kline-fullscreen')) {
        // 退出全屏
        klinePanel.classList.remove('kline-fullscreen');
        document.body.style.overflow = '';
    } else {
        // 进入全屏
        klinePanel.classList.add('kline-fullscreen');
        document.body.style.overflow = 'hidden';
    }

    // 调整图表大小
    setTimeout(() => {
        if (window.KLINE_CONFIG?.chart) {
            const container = window.KLINE_CONFIG.container;
            window.KLINE_CONFIG.chart.applyOptions({
                width: container.clientWidth,
                height: container.clientHeight
            });
        }
    }, 100);
}

// 测试TG机器人通知功能
async function testTelegramNotifications() {
    try {
        // 获取当前BTC价格用于真实数据
        const btcCrypto = cryptos.find(c => c.symbol === 'BTCUSDT');
        const currentPrice = btcCrypto ? btcCrypto.price : 100000;

        // 1. 发送系统启动通知
        const systemStartMessage = `
🚀 *断金交易系统v2.0启动*

📅 启动时间: ${new Date().toLocaleString('zh-CN')}
💰 支持币种: ${cryptos.length}种
🔗 API状态: ${window.apiStatus?.connected ? '已连接' : '未连接'}
📊 账户余额: ${window.spotAccountBalance?.totalBalance?.toFixed(2) || '0.00'} USDT

📈 当前BTC价格: $${formatPrice(currentPrice)}
🎯 K线图: 已集成
⚡ 支撑阻力位: 自动检测中
🤖 TG机器人: 测试中...

系统已准备就绪，开始监控市场...
        `;

        if (window.sendTelegramMessage) {
            await window.sendTelegramMessage(systemStartMessage);
            console.log('✅ 系统启动通知已发送');
        }

        // 2. 延迟发送技术分析测试
        setTimeout(async () => {
            const techAnalysisMessage = `
🎯 *技术分析测试*

📊 交易对: BTC/USDT
💲 当前价格: $${formatPrice(currentPrice)}
📍 测试功能: 支撑阻力位检测

🔍 技术分析:
• 第一支撑: $${formatPrice(currentPrice * 0.98)}
• 第二支撑: $${formatPrice(currentPrice * 0.96)}
• 第一阻力: $${formatPrice(currentPrice * 1.02)}
• 第二阻力: $${formatPrice(currentPrice * 1.04)}

📈 建议操作: 测试模式运行中
⚡ 强度评分: 8/10
🕐 时间框架: 1小时
⏰ 时间: ${new Date().toLocaleString('zh-CN')}

🤖 TG机器人功能测试完成！
            `;

            if (window.sendTelegramMessage) {
                await window.sendTelegramMessage(techAnalysisMessage);
                console.log('✅ 技术分析测试通知已发送');
            }
        }, 3000);

        // 3. 延迟发送连接状态测试
        setTimeout(async () => {
            const connectionMessage = `
🔗 *连接状态测试*

📡 API连接: ${window.apiStatus?.connected ? '✅ 正常' : '❌ 断开'}
🌐 WebSocket: ${window.apiStatus?.websocketConnected ? '✅ 已连接' : '❌ 未连接'}
📊 数据更新: ✅ 正常
⚡ 延迟: <100ms

🔄 重连次数: 0
📈 成功率: 100%
⏰ 时间: ${new Date().toLocaleString('zh-CN')}

🎉 所有TG通知功能测试完成！
系统运行状态良好，准备开始实盘交易监控。
            `;

            if (window.sendTelegramMessage) {
                await window.sendTelegramMessage(connectionMessage);
                console.log('✅ 连接状态测试通知已发送');
            }
        }, 6000);

    } catch (error) {
        console.error('❌ TG通知测试失败:', error);
    }
}

// 切换交易类型
function switchTradeType(type) {
    currentTradeType = type;

    // 更新标签样式
    const spotTab = document.getElementById('spotTradeTab');
    const marginTab = document.getElementById('marginTradeTab');

    if (type === 'spot') {
        spotTab.classList.add('active');
        marginTab.classList.remove('active');
        allTradeRecords = spotTradeRecords;
    } else {
        marginTab.classList.add('active');
        spotTab.classList.remove('active');
        allTradeRecords = marginTradeRecords;
    }

    // 重置分页
    totalRecords = allTradeRecords.length;
    currentPage = 1;

    // 更新显示
    updateTradeHistoryDisplay();
    updatePaginationControls();

    console.log(`📊 切换到${type === 'spot' ? '现货' : '杠杆'}交易记录: ${totalRecords} 条`);
}

// 定期刷新交易记录
function startTradeHistoryUpdates() {
    // 每5分钟刷新一次交易记录
    setInterval(async () => {
        if (window.apiStatus?.connected) {
            console.log('🔄 定期刷新交易记录...');
            try {
                await loadRealTradeHistory();
                updateTradeHistoryDisplay();
                updatePaginationControls();
                console.log('✅ 交易记录刷新完成');
            } catch (error) {
                console.error('❌ 交易记录刷新失败:', error);
            }
        }
    }, 5 * 60 * 1000); // 5分钟
}

// 切换账户类型显示
function switchAccountType(type) {
    const spotTab = document.getElementById('spotAccountTab');
    const marginTab = document.getElementById('marginAccountTab');
    const spotDetails = document.getElementById('spotBalanceDetails');
    const marginDetails = document.getElementById('marginBalanceDetails');
    const currentBalance = document.getElementById('currentTotalBalance');
    const currentChange = document.getElementById('currentBalanceChange');

    if (type === 'spot') {
        // 切换到现货
        spotTab.classList.add('active');
        marginTab.classList.remove('active');
        spotDetails.style.display = 'block';
        marginDetails.style.display = 'none';

        // 更新余额显示
        const spotBalance = document.getElementById('spotAvailableBalance')?.textContent || '$0.00';
        currentBalance.textContent = spotBalance;
        currentChange.textContent = '现货账户 24h变化 --';

    } else {
        // 切换到杠杆
        marginTab.classList.add('active');
        spotTab.classList.remove('active');
        marginDetails.style.display = 'block';
        spotDetails.style.display = 'none';

        // 更新余额显示
        const marginBalance = document.getElementById('marginNetAsset')?.textContent || '$0.00';
        currentBalance.textContent = marginBalance;
        currentChange.textContent = '杠杆账户 24h变化 --';
    }

    console.log(`💰 切换到${type === 'spot' ? '现货' : '杠杆'}账户显示`);
}

// 简单K线图备用方案
function showSimpleKlineChart() {
    const container = document.getElementById('klineChartContainer');
    if (!container) return;

    console.log('📊 显示简单K线图...');

    // 创建简单的Canvas K线图
    container.innerHTML = '';
    const canvas = document.createElement('canvas');
    canvas.width = container.clientWidth || 800;
    canvas.height = container.clientHeight || 400;
    canvas.style.width = '100%';
    canvas.style.height = '100%';
    canvas.style.background = '#1a1a1a';
    container.appendChild(canvas);

    const ctx = canvas.getContext('2d');

    // 生成简单的模拟数据
    const data = [];
    let price = 100000; // BTC起始价格

    for (let i = 0; i < 50; i++) {
        const change = (Math.random() - 0.5) * 2000; // ±1000的变化
        const open = price;
        const close = price + change;
        const high = Math.max(open, close) + Math.random() * 500;
        const low = Math.min(open, close) - Math.random() * 500;

        data.push({ open, high, low, close });
        price = close;
    }

    // 绘制简单K线
    const width = canvas.width;
    const height = canvas.height;
    const padding = 40;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;

    // 计算价格范围
    const prices = data.flatMap(d => [d.high, d.low]);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;

    // 绘制背景网格
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;

    // 水平网格线
    for (let i = 0; i <= 5; i++) {
        const y = padding + (chartHeight / 5) * i;
        ctx.beginPath();
        ctx.moveTo(padding, y);
        ctx.lineTo(width - padding, y);
        ctx.stroke();
    }

    // 垂直网格线
    for (let i = 0; i <= 10; i++) {
        const x = padding + (chartWidth / 10) * i;
        ctx.beginPath();
        ctx.moveTo(x, padding);
        ctx.lineTo(x, height - padding);
        ctx.stroke();
    }

    // 绘制K线
    const candleWidth = chartWidth / data.length * 0.8;
    const candleSpacing = chartWidth / data.length;

    data.forEach((candle, index) => {
        const x = padding + index * candleSpacing + candleSpacing / 2;

        // 计算Y坐标
        const openY = padding + (maxPrice - candle.open) / priceRange * chartHeight;
        const highY = padding + (maxPrice - candle.high) / priceRange * chartHeight;
        const lowY = padding + (maxPrice - candle.low) / priceRange * chartHeight;
        const closeY = padding + (maxPrice - candle.close) / priceRange * chartHeight;

        // 确定颜色
        const isRising = candle.close > candle.open;
        const color = isRising ? '#00d4aa' : '#ff6b6b';

        // 绘制影线
        ctx.strokeStyle = color;
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(x, highY);
        ctx.lineTo(x, lowY);
        ctx.stroke();

        // 绘制实体
        ctx.fillStyle = color;
        const bodyTop = Math.min(openY, closeY);
        const bodyHeight = Math.abs(closeY - openY);
        ctx.fillRect(x - candleWidth / 2, bodyTop, candleWidth, Math.max(bodyHeight, 1));
    });

    // 绘制价格标签
    ctx.fillStyle = '#d1d4dc';
    ctx.font = '12px Arial';
    ctx.textAlign = 'right';

    for (let i = 0; i <= 5; i++) {
        const price = maxPrice - (priceRange * i / 5);
        const y = padding + (chartHeight / 5) * i;
        ctx.fillText('$' + price.toFixed(0), width - 5, y + 4);
    }

    // 绘制标题
    ctx.fillStyle = '#00d4aa';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'left';
    ctx.fillText('📈 BTC/USDT K线图 (简化版)', padding, 25);

    // 绘制当前价格
    const currentPrice = data[data.length - 1].close;
    ctx.fillStyle = '#ffd700';
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'right';
    ctx.fillText(`当前价格: $${currentPrice.toFixed(2)}`, width - padding, 25);

    console.log('✅ 简单K线图绘制完成');
}

// 直接加载实盘K线数据
async function loadRealKlineData(symbol, timeframe) {
    try {
        console.log(`📊 加载实盘K线数据: ${symbol} ${timeframe}`);

        // 直接调用币安API
        const response = await fetch(`/api/binance/klines?symbol=${symbol}&interval=${timeframe}&limit=50`);
        const data = await response.json();

        if (!data.success) {
            throw new Error(data.error || '获取K线数据失败');
        }

        console.log(`✅ 成功获取实盘K线数据: ${data.data.length} 条`);

        // 绘制实盘K线图
        drawRealKlineChart(data.data, symbol);

    } catch (error) {
        console.error('❌ 实盘K线数据加载失败:', error);
        // 备用方案
        showSimpleKlineChart();
    }
}

// 绘制实盘K线图 - 高清版本
function drawRealKlineChart(klineData, symbol) {
    const container = document.getElementById('klineChartContainer');
    if (!container) return;

    console.log('🎨 绘制高清实盘K线图...');

    // 清空容器
    container.innerHTML = '';

    // 创建高清Canvas
    const canvas = document.createElement('canvas');
    const rect = container.getBoundingClientRect();
    const dpr = window.devicePixelRatio || 1;

    // 设置高清显示
    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;
    canvas.style.width = rect.width + 'px';
    canvas.style.height = rect.height + 'px';
    canvas.style.background = '#1a1a1a';
    container.appendChild(canvas);

    // 保存canvas引用供后续更新使用
    window.klineCanvas = canvas;
    window.klineSymbol = symbol;

    const ctx = canvas.getContext('2d');
    const dpr = window.devicePixelRatio || 1;
    ctx.scale(dpr, dpr);

    // 转换币安数据格式
    const candleData = klineData.map(kline => ({
        time: kline[0],
        open: parseFloat(kline[1]),
        high: parseFloat(kline[2]),
        low: parseFloat(kline[3]),
        close: parseFloat(kline[4]),
        volume: parseFloat(kline[5])
    }));

    // 保存数据供后续更新使用
    window.klineData = candleData;

    // 计算价格范围
    const prices = candleData.flatMap(d => [d.high, d.low]);
    const minPrice = Math.min(...prices);
    const maxPrice = Math.max(...prices);
    const priceRange = maxPrice - minPrice;

    const width = canvas.width / dpr;
    const height = canvas.height / dpr;
    const padding = 60;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;

    // 绘制背景
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, width, height);

    // 绘制网格
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;

    // 水平网格线和价格标签
    for (let i = 0; i <= 5; i++) {
        const y = padding + (chartHeight / 5) * i;
        const price = maxPrice - (priceRange * i / 5);

        // 网格线
        ctx.beginPath();
        ctx.moveTo(padding, y);
        ctx.lineTo(width - padding, y);
        ctx.stroke();

        // 价格标签
        ctx.fillStyle = '#888';
        ctx.font = '12px Arial';
        ctx.textAlign = 'right';
        ctx.fillText('$' + price.toFixed(2), padding - 10, y + 4);
    }

    // 垂直网格线
    for (let i = 0; i <= 10; i++) {
        const x = padding + (chartWidth / 10) * i;
        ctx.beginPath();
        ctx.moveTo(x, padding);
        ctx.lineTo(x, height - padding);
        ctx.stroke();
    }

    // 绘制K线
    const candleWidth = chartWidth / candleData.length * 0.8;
    const candleSpacing = chartWidth / candleData.length;

    candleData.forEach((candle, index) => {
        const x = padding + index * candleSpacing + candleSpacing / 2;

        // 计算Y坐标
        const openY = padding + (maxPrice - candle.open) / priceRange * chartHeight;
        const highY = padding + (maxPrice - candle.high) / priceRange * chartHeight;
        const lowY = padding + (maxPrice - candle.low) / priceRange * chartHeight;
        const closeY = padding + (maxPrice - candle.close) / priceRange * chartHeight;

        // 确定颜色
        const isRising = candle.close > candle.open;
        const color = isRising ? '#00d4aa' : '#ff6b6b';

        // 绘制影线
        ctx.strokeStyle = color;
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(x, highY);
        ctx.lineTo(x, lowY);
        ctx.stroke();

        // 绘制实体
        ctx.fillStyle = color;
        const bodyTop = Math.min(openY, closeY);
        const bodyHeight = Math.abs(closeY - openY);
        ctx.fillRect(x - candleWidth / 2, bodyTop, candleWidth, Math.max(bodyHeight, 1));
    });

    // 绘制标题
    ctx.fillStyle = '#00d4aa';
    ctx.font = 'bold 16px Arial';
    ctx.textAlign = 'left';
    ctx.fillText(`📈 ${symbol} 实盘K线图`, padding, 30);

    // 绘制当前价格
    const currentPrice = candleData[candleData.length - 1].close;
    ctx.fillStyle = '#ffd700';
    ctx.font = 'bold 14px Arial';
    ctx.textAlign = 'right';
    ctx.fillText(`实时价格: $${currentPrice.toFixed(2)}`, width - padding, 30);

    // 绘制数据来源
    ctx.fillStyle = '#666';
    ctx.font = '10px Arial';
    ctx.textAlign = 'right';
    ctx.fillText('数据来源: 币安API', width - padding, height - 10);

    console.log(`✅ 实盘K线图绘制完成 - 当前价格: $${currentPrice.toFixed(2)}`);
}

// 刷新账户余额
async function refreshAccountBalances() {
    try {
        console.log('💰 刷新账户余额...');

        if (!window.apiStatus?.connected) {
            console.log('⚠️ API未连接，跳过余额刷新');
            return;
        }

        // 获取现货账户余额
        if (window.fetchSpotAccountInfo) {
            console.log('🔄 获取现货账户余额...');
            await window.fetchSpotAccountInfo();
        }

        // 获取杠杆账户余额
        if (window.fetchMarginAccountInfo) {
            console.log('🔄 获取杠杆账户余额...');
            await window.fetchMarginAccountInfo();
        }

        // 更新UI显示
        if (window.updateBalanceDisplay) {
            console.log('🔄 更新余额显示...');
            window.updateBalanceDisplay();
        }

        console.log('✅ 账户余额刷新完成');

    } catch (error) {
        console.error('❌ 账户余额刷新失败:', error);
    }
}

// K线图周期切换
async function switchKlineTimeframe(timeframe) {
    console.log(`📊 切换K线周期: ${timeframe}`);

    const symbol = window.klineSymbol || 'BTCUSDT';
    await loadRealKlineData(symbol, timeframe);

    // 更新选择器状态
    const selector = document.getElementById('klineTimeframeSelect');
    if (selector) {
        selector.value = timeframe;
    }
}

// K线图交易对切换
async function switchKlineSymbol(symbol) {
    console.log(`📊 切换K线交易对: ${symbol}`);

    const timeframe = document.getElementById('klineTimeframeSelect')?.value || '1h';
    await loadRealKlineData(symbol, timeframe);

    // 更新选择器状态
    const selector = document.getElementById('klineSymbolSelect');
    if (selector) {
        selector.value = symbol;
    }
}

// 启动K线图实时更新
function startKlineRealTimeUpdates() {
    // 每30秒更新一次K线数据
    setInterval(async () => {
        if (window.klineSymbol) {
            const timeframe = document.getElementById('klineTimeframeSelect')?.value || '1h';
            console.log('🔄 自动更新K线数据...');
            await loadRealKlineData(window.klineSymbol, timeframe);
        }
    }, 30000);
}

// 绑定K线图控制事件
function bindKlineEvents() {
    const symbolSelect = document.getElementById('klineSymbolSelect');
    const timeframeSelect = document.getElementById('klineTimeframeSelect');

    if (symbolSelect) {
        symbolSelect.addEventListener('change', (e) => {
            switchKlineSymbol(e.target.value);
        });
    }

    if (timeframeSelect) {
        timeframeSelect.addEventListener('change', (e) => {
            switchKlineTimeframe(e.target.value);
        });
    }

    console.log('✅ K线图控制事件已绑定');
}

// 导出全局函数
window.switchTradeType = switchTradeType;
window.switchAccountType = switchAccountType;
window.showSimpleKlineChart = showSimpleKlineChart;
window.loadRealKlineData = loadRealKlineData;
window.refreshAccountBalances = refreshAccountBalances;
window.switchKlineTimeframe = switchKlineTimeframe;
window.switchKlineSymbol = switchKlineSymbol;

// 页面加载完成后初始化分页
document.addEventListener('DOMContentLoaded', function() {
    // 启动定期刷新
    setTimeout(() => {
        startTradeHistoryUpdates();
    }, 10000); // 10秒后开始定期刷新
});

// 初始化表单事件监听器
function initializeModalForms() {
    // 币安登录表单提交
    const binanceForm = document.getElementById('binanceLoginForm');
    if (binanceForm) {
        binanceForm.addEventListener('submit', async function(e) {
            e.preventDefault();
            e.stopPropagation();

            try {
                console.log('📝 开始处理币安API表单提交...');

                // 禁用提交按钮防止重复提交
                const submitBtn = e.target.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = true;
                    submitBtn.textContent = '连接中...';
                }

                const apiKey = document.getElementById('binanceApiKey').value.trim();
                const apiSecret = document.getElementById('binanceApiSecret').value.trim();
                const useTestnet = document.getElementById('binanceTestnet').checked;

                console.log('🔍 表单数据验证:', {
                    apiKeyLength: apiKey.length,
                    apiSecretLength: apiSecret.length,
                    useTestnet: useTestnet
                });

                if (!apiKey || !apiSecret) {
                    console.error('❌ 表单验证失败: API Key或Secret为空');
                    alert('请输入完整的API Key和Secret');
                    return;
                }

                if (apiKey.length < 10 || apiSecret.length < 10) {
                    console.error('❌ 表单验证失败: API Key或Secret长度不足');
                    alert('API Key和Secret长度不足，请检查是否完整复制');
                    return;
                }

                // 保存API配置到localStorage（仅保存API Key，Secret不保存）
                try {
                    localStorage.setItem('binance_api_key', apiKey);
                    localStorage.setItem('binance_use_testnet', useTestnet.toString());
                    console.log('💾 API配置已保存到localStorage');
                } catch (storageError) {
                    console.warn('⚠️ localStorage保存失败:', storageError);
                }

                // 尝试连接币安API
                console.log('🔗 开始连接币安API...');
                const success = await connectBinanceAPI(apiKey, apiSecret, useTestnet);

                if (success) {
                    closeBinanceModal();
                    console.log('✅ 币安API连接成功');
                    alert('币安API连接成功！');
                } else {
                    console.error('❌ 币安API连接失败');
                    alert('币安API连接失败，请检查API Key和Secret是否正确');
                }
            } catch (error) {
                console.error('❌ 表单提交处理异常:', error);
                console.error('❌ 错误堆栈:', error.stack);
                alert('表单提交失败: ' + error.message);
            } finally {
                // 恢复提交按钮状态
                const submitBtn = e.target.querySelector('button[type="submit"]');
                if (submitBtn) {
                    submitBtn.disabled = false;
                    submitBtn.textContent = '连接';
                }
            }
        });
    }

    // Telegram登录表单提交
    const telegramForm = document.getElementById('telegramLoginForm');
    if (telegramForm) {
        telegramForm.addEventListener('submit', async function(e) {
            e.preventDefault();

            const botToken = document.getElementById('telegramBotToken').value.trim();
            const chatId = document.getElementById('telegramChatId').value.trim();

            if (!botToken || !chatId) {
                alert('请输入完整的Bot Token和Chat ID');
                return;
            }

            // 保存Telegram配置到localStorage
            localStorage.setItem('telegram_bot_token', botToken);
            localStorage.setItem('telegram_chat_id', chatId);

            // 尝试连接Telegram机器人
            const success = await connectTelegramBot(botToken, chatId);

            if (success) {
                closeTelegramModal();
                console.log('✅ Telegram机器人连接成功');
            } else {
                alert('Telegram机器人连接失败，请检查Bot Token和Chat ID是否正确');
            }
        });
    }

    // 点击模态框外部关闭
    window.addEventListener('click', function(event) {
        const binanceModal = document.getElementById('binanceLoginModal');
        const telegramModal = document.getElementById('telegramLoginModal');

        if (event.target === binanceModal) {
            closeBinanceModal();
        }
        if (event.target === telegramModal) {
            closeTelegramModal();
        }
    });
}

// 连接币安API
async function connectBinanceAPI(apiKey, apiSecret, useTestnet = false) {
    try {
        console.log('🔗 正在连接币安API...', { apiKey: apiKey.substring(0, 8) + '...', useTestnet });

        // 保存API配置到全局变量
        window.binanceConfig = {
            apiKey: apiKey,
            apiSecret: apiSecret,
            useTestnet: useTestnet
        };

        // 首先检查网络连接
        console.log('🌐 检查网络连接...');
        try {
            const connectivityResponse = await fetch('http://localhost:3001/api/binance/connectivity');
            const connectivityResult = await connectivityResponse.json();

            if (connectivityResult.success) {
                const workingDomains = connectivityResult.results.filter(r => r.status === 'success');
                console.log(`✅ 网络连接正常，${workingDomains.length}/4 个域名可用`);
                console.log('🚀 推荐域名:', connectivityResult.recommendation);
            } else {
                console.warn('⚠️ 网络连接检查失败，但继续尝试API连接');
            }
        } catch (error) {
            console.warn('⚠️ 网络连接检查失败:', error.message);
        }

        // 然后测试API连接
        console.log('🧪 测试API连接...');
        const testResponse = await fetch('http://localhost:3001/api/binance/test', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                apiKey: apiKey,
                apiSecret: apiSecret,
                useTestnet: useTestnet
            })
        });

        const testResult = await testResponse.json();
        if (!testResult.success) {
            // 提供更详细的错误信息
            let errorMessage = testResult.error || 'API连接测试失败';

            if (errorMessage.includes('API-key format invalid')) {
                errorMessage = 'API Key格式无效，请检查是否正确复制了完整的API Key';
            } else if (errorMessage.includes('Signature for this request is not valid')) {
                errorMessage = 'API Secret无效，请检查是否正确复制了完整的API Secret';
            } else if (errorMessage.includes('IP not in whitelist')) {
                errorMessage = 'IP地址未在白名单中，请在币安API管理中添加您的IP地址';
            } else if (errorMessage.includes('Invalid API-key, IP, or permissions')) {
                errorMessage = 'API Key权限不足，请确保启用了现货交易权限';
            }

            throw new Error(errorMessage);
        }

        console.log('✅ API连接测试成功');

        // 设置API配置到后端
        const configResponse = await fetch('http://localhost:3001/api/binance/config', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                apiKey: apiKey,
                apiSecret: apiSecret,
                useTestnet: useTestnet
            })
        });

        const configResult = await configResponse.json();
        if (!configResult.success) {
            throw new Error(configResult.error || '设置API配置失败');
        }

        console.log('✅ API配置已发送到后端');

        // 调用真实的币安API初始化
        let success = false;
        try {
            success = await initializeBinanceAPI();
            if (!success) {
                // initializeBinanceAPI返回false，不是抛出异常
                throw new Error('API初始化返回失败');
            }
        } catch (error) {
            console.error('❌ 真实API连接失败:', error);
            // 不使用模拟数据，保持未连接状态
            success = false;
            window.apiStatus.connected = false;

            // 清空账户余额数据
            window.spotAccountBalance = null;
            window.marginAccountBalance = null;
            window.accountBalance = null;

            console.log('💡 请点击"币安API"按钮配置API密钥以连接真实账户');
        }

        if (success) {
            // 更新连接状态
            apiStatus.binance.connected = true;
            apiStatus.binance.lastCheck = new Date();
            updateConnectionStatus('binance', true);

            // 获取账户信息和余额
            console.log('🔄 开始获取账户信息...');
            let accountSuccess = true;

            // 强制重新获取现货账户信息
            console.log('📊 获取现货账户信息...');
            accountSuccess = await fetchSpotAccountInfo();

            // 强制重新获取杠杆账户信息
            console.log('⚡ 获取杠杆账户信息...');
            await fetchMarginAccountInfo();

            // 调试输出
            console.log('🔍 调试信息:');
            console.log('- 现货账户余额:', window.spotAccountBalance);
            console.log('- 杠杆账户余额:', window.marginAccountBalance);
            console.log('- updateBalanceDisplay函数:', typeof window.updateBalanceDisplay);

            // 强制更新余额显示
            if (window.updateBalanceDisplay) {
                console.log('🔄 调用updateBalanceDisplay...');
                window.updateBalanceDisplay();
            } else {
                console.error('❌ updateBalanceDisplay函数未定义');
            }

            // 延迟再次更新，确保UI刷新
            setTimeout(() => {
                if (window.updateBalanceDisplay) {
                    console.log('🔄 延迟调用updateBalanceDisplay...');
                    window.updateBalanceDisplay();
                } else {
                    console.error('❌ updateBalanceDisplay函数未定义（延迟调用）');
                }
            }, 1000);

            console.log('✅ 币安API连接成功，账户信息已更新');
            console.log('💰 现货账户余额:', window.spotAccountBalance);
            console.log('⚡ 杠杆账户余额:', window.marginAccountBalance);
            return true;
        } else {
            console.warn('⚠️ API连接失败');
            return false;
        }

    } catch (error) {
        console.error('❌ 币安API连接失败:', error);
        console.error('❌ 错误堆栈:', error.stack);

        // 更新连接状态
        apiStatus.binance.connected = false;
        apiStatus.binance.lastError = error.message;
        updateConnectionStatus('binance', false);

        // 显示详细错误信息
        let userMessage = '币安API连接失败: ' + error.message;

        // 根据错误类型提供建议
        if (error.message.includes('网络') || error.message.includes('fetch') || error.message.includes('Failed to fetch')) {
            userMessage += '\n\n建议:\n1. 检查网络连接\n2. 确认服务器是否运行在3001端口\n3. 点击"网络诊断"检查连接状态';
        } else if (error.message.includes('API')) {
            userMessage += '\n\n建议:\n1. 检查API Key和Secret是否正确\n2. 确认API权限设置\n3. 检查IP白名单设置';
        } else if (error.message.includes('Invalid Date')) {
            userMessage += '\n\n这是一个日期处理错误，请刷新页面重试';
        }

        console.log('💡 用户提示:', userMessage);

        // 清理可能的无效状态
        window.apiStatus.connected = false;
        window.spotAccountBalance = null;
        window.marginAccountBalance = null;
        window.accountBalance = null;

        return false;
    }
}

// 连接Telegram机器人
async function connectTelegramBot(botToken, chatId) {
    try {
        // 这里调用实际的Telegram机器人连接逻辑
        console.log('🔗 正在连接Telegram机器人...', { chatId });

        // 模拟连接延迟
        await new Promise(resolve => setTimeout(resolve, 1500));

        // 更新连接状态
        apiStatus.telegram.connected = true;
        apiStatus.telegram.lastCheck = new Date();
        updateConnectionStatus('telegram', true);

        return true;
    } catch (error) {
        console.error('❌ Telegram机器人连接失败:', error);
        return false;
    }
}

// 网络连接诊断
async function testNetworkConnectivity() {
    try {
        console.log('🌐 开始网络诊断...');

        const response = await fetch('http://localhost:3001/api/binance/connectivity');
        const result = await response.json();

        if (result.success) {
            const workingDomains = result.results.filter(r => r.status === 'success');
            const failedDomains = result.results.filter(r => r.status === 'failed');

            let message = `网络诊断结果:\n\n`;
            message += `✅ 可用域名: ${workingDomains.length}/4\n`;

            workingDomains.forEach(domain => {
                message += `  • ${domain.url} (${domain.latency})\n`;
            });

            if (failedDomains.length > 0) {
                message += `\n❌ 不可用域名: ${failedDomains.length}\n`;
                failedDomains.forEach(domain => {
                    message += `  • ${domain.url} - ${domain.error}\n`;
                });
            }

            message += `\n🚀 推荐使用: ${result.recommendation}`;

            alert(message);
        } else {
            alert('网络诊断失败: ' + result.error);
        }
    } catch (error) {
        console.error('❌ 网络诊断失败:', error);
        alert('网络诊断失败: ' + error.message);
    }
}

// 将需要在HTML中调用的函数暴露到全局作用域
window.toggleBinanceConnection = toggleBinanceConnection;
window.toggleTelegramConnection = toggleTelegramConnection;
window.showBinanceModal = showBinanceModal;
window.closeBinanceModal = closeBinanceModal;
window.showTelegramModal = showTelegramModal;
window.closeTelegramModal = closeTelegramModal;
window.testNetworkConnectivity = testNetworkConnectivity;
window.clearSavedApiConfig = clearSavedApiConfig;

console.log('🧠 主程序模块已加载');
console.log('💡 调试提示：如果API Key显示异常，请在控制台运行 clearSavedApiConfig() 清除保存的配置');
