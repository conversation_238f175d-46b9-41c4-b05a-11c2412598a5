// 断金ใ现货'杠杆自动化交易系统 - 高级K线图模块
// 功能：TradingView风格的专业K线图，支撑阻力位标注，多时间框架
// 特性：自适应大小，实时数据，技术指标叠加

// K线图配置
const KLINE_CONFIG = {
    container: null,
    chart: null,
    candlestickSeries: null,
    volumeSeries: null,
    supportLines: [],
    resistanceLines: [],
    currentSymbol: 'BTCUSDT',
    currentTimeframe: '1h',
    autoResize: true,
    showVolume: true,
    showIndicators: true
};

// 时间框架配置
const TIMEFRAMES = {
    '1m': { label: '1分钟', interval: '1m', limit: 100 },
    '5m': { label: '5分钟', interval: '5m', limit: 100 },
    '15m': { label: '15分钟', interval: '15m', limit: 100 },
    '1h': { label: '1小时', interval: '1h', limit: 100 },
    '4h': { label: '4小时', interval: '4h', limit: 100 },
    '1d': { label: '1天', interval: '1d', limit: 100 },
    '1w': { label: '1周', interval: '1w', limit: 100 }
};

// 初始化高级K线图 - 简化版本
async function initializeAdvancedKlineChart(containerId, symbol = 'BTCUSDT') {
    try {
        console.log('🚀 初始化高级K线图...');

        const container = document.getElementById(containerId);
        if (!container) {
            throw new Error(`容器 ${containerId} 不存在`);
        }

        KLINE_CONFIG.container = container;
        KLINE_CONFIG.currentSymbol = symbol;

        // 显示加载状态
        initKlineDisplay();

        // 延迟创建Canvas，确保容器准备好
        setTimeout(() => {
            // 清空容器并创建Canvas
            container.innerHTML = '';
            const canvas = document.createElement('canvas');
            canvas.id = 'klineCanvas';
            canvas.style.width = '100%';
            canvas.style.height = '100%';
            canvas.style.background = '#1a1a1a';
            container.appendChild(canvas);

            KLINE_CONFIG.canvas = canvas;
            KLINE_CONFIG.ctx = canvas.getContext('2d');

            // 设置Canvas尺寸
            resizeCanvas();

            // 加载数据
            loadKlineData(symbol, KLINE_CONFIG.currentTimeframe);
        }, 500);

        // 设置自动调整大小
        if (KLINE_CONFIG.autoResize) {
            setupAutoResize();
        }

        // 启动实时数据更新
        startRealTimeUpdates();

        console.log('✅ 高级K线图初始化完成');
        return true;

    } catch (error) {
        console.error('❌ 高级K线图初始化失败:', error);
        // 显示错误信息
        showKlineError(containerId, error.message);
        return false;
    }
}

// 调整Canvas尺寸
function resizeCanvas() {
    const canvas = KLINE_CONFIG.canvas;
    const container = KLINE_CONFIG.container;

    if (canvas && container) {
        const rect = container.getBoundingClientRect();
        canvas.width = rect.width * window.devicePixelRatio;
        canvas.height = rect.height * window.devicePixelRatio;
        canvas.style.width = rect.width + 'px';
        canvas.style.height = rect.height + 'px';

        const ctx = KLINE_CONFIG.ctx;
        ctx.scale(window.devicePixelRatio, window.devicePixelRatio);
    }
}

// 显示K线图错误
function showKlineError(containerId, errorMessage) {
    const container = document.getElementById(containerId);
    if (container) {
        container.innerHTML = `
            <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; color: #ff6b6b; background: #1a1a1a;">
                <div style="font-size: 24px; margin-bottom: 10px;">⚠️</div>
                <div style="font-size: 14px; text-align: center;">
                    K线图加载失败<br>
                    <small style="color: #888;">${errorMessage}</small>
                </div>
            </div>
        `;
    }
}

// 初始化K线图显示
function initKlineDisplay() {
    const container = KLINE_CONFIG.container;
    if (!container) return;

    // 显示加载状态
    container.innerHTML = `
        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center; height: 100%; color: #00d4aa; background: #1a1a1a;">
            <div style="font-size: 24px; margin-bottom: 10px;">📈</div>
            <div style="font-size: 14px; text-align: center;">
                正在加载K线数据...<br>
                <small style="color: #888;">请稍候</small>
            </div>
        </div>
    `;
}

// 绘制K线图
function drawKlineChart(klineData) {
    const canvas = KLINE_CONFIG.canvas;
    const ctx = KLINE_CONFIG.ctx;

    if (!canvas || !ctx) {
        console.log('⚠️ Canvas未准备好，延迟绘制');
        setTimeout(() => drawKlineChart(klineData), 100);
        return;
    }

    if (!klineData || klineData.length === 0) {
        console.log('⚠️ 无K线数据，使用模拟数据');
        klineData = generateMockKlineData(KLINE_CONFIG.currentSymbol);
    }

    // 清空画布
    ctx.clearRect(0, 0, canvas.width / window.devicePixelRatio, canvas.height / window.devicePixelRatio);

    const width = canvas.width / window.devicePixelRatio;
    const height = canvas.height / window.devicePixelRatio;

    // 绘制背景
    ctx.fillStyle = '#1a1a1a';
    ctx.fillRect(0, 0, width, height);

    // 计算价格范围
    let minPrice = Infinity;
    let maxPrice = -Infinity;

    klineData.forEach(candle => {
        minPrice = Math.min(minPrice, parseFloat(candle.low));
        maxPrice = Math.max(maxPrice, parseFloat(candle.high));
    });

    const priceRange = maxPrice - minPrice;
    const padding = 20;
    const chartWidth = width - padding * 2;
    const chartHeight = height - padding * 2;

    // 绘制网格线
    drawGrid(ctx, padding, chartWidth, chartHeight, minPrice, maxPrice);

    // 绘制K线
    const candleWidth = chartWidth / klineData.length * 0.8;
    const candleSpacing = chartWidth / klineData.length;

    klineData.forEach((candle, index) => {
        const x = padding + index * candleSpacing + candleSpacing / 2;
        const open = parseFloat(candle.open);
        const high = parseFloat(candle.high);
        const low = parseFloat(candle.low);
        const close = parseFloat(candle.close);

        // 计算Y坐标
        const openY = padding + (maxPrice - open) / priceRange * chartHeight;
        const highY = padding + (maxPrice - high) / priceRange * chartHeight;
        const lowY = padding + (maxPrice - low) / priceRange * chartHeight;
        const closeY = padding + (maxPrice - close) / priceRange * chartHeight;

        // 确定颜色（涨绿跌红）
        const isRising = close > open;
        const candleColor = isRising ? '#00d4aa' : '#ff6b6b';

        // 绘制影线
        ctx.strokeStyle = candleColor;
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(x, highY);
        ctx.lineTo(x, lowY);
        ctx.stroke();

        // 绘制实体
        ctx.fillStyle = candleColor;
        const bodyTop = Math.min(openY, closeY);
        const bodyHeight = Math.abs(closeY - openY);
        ctx.fillRect(x - candleWidth / 2, bodyTop, candleWidth, Math.max(bodyHeight, 1));
    });

    // 绘制价格标签
    drawPriceLabels(ctx, width, height, minPrice, maxPrice, padding);

    // 绘制当前价格
    if (klineData.length > 0) {
        const currentPrice = parseFloat(klineData[klineData.length - 1].close);
        drawCurrentPrice(ctx, width, height, currentPrice, minPrice, maxPrice, padding);
    }
}

// 绘制网格线
function drawGrid(ctx, padding, chartWidth, chartHeight, minPrice, maxPrice) {
    ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
    ctx.lineWidth = 1;

    // 水平网格线
    for (let i = 0; i <= 5; i++) {
        const y = padding + (chartHeight / 5) * i;
        ctx.beginPath();
        ctx.moveTo(padding, y);
        ctx.lineTo(padding + chartWidth, y);
        ctx.stroke();
    }

    // 垂直网格线
    for (let i = 0; i <= 10; i++) {
        const x = padding + (chartWidth / 10) * i;
        ctx.beginPath();
        ctx.moveTo(x, padding);
        ctx.lineTo(x, padding + chartHeight);
        ctx.stroke();
    }
}

// 绘制价格标签
function drawPriceLabels(ctx, width, height, minPrice, maxPrice, padding) {
    ctx.fillStyle = '#d1d4dc';
    ctx.font = '12px Arial';
    ctx.textAlign = 'right';

    for (let i = 0; i <= 5; i++) {
        const price = maxPrice - (maxPrice - minPrice) * (i / 5);
        const y = padding + (height - padding * 2) / 5 * i;
        ctx.fillText('$' + price.toFixed(2), width - 5, y + 4);
    }
}

// 绘制当前价格线
function drawCurrentPrice(ctx, width, height, currentPrice, minPrice, maxPrice, padding) {
    const priceRange = maxPrice - minPrice;
    const y = padding + (maxPrice - currentPrice) / priceRange * (height - padding * 2);

    // 绘制价格线
    ctx.strokeStyle = '#ffd700';
    ctx.lineWidth = 2;
    ctx.setLineDash([5, 5]);
    ctx.beginPath();
    ctx.moveTo(padding, y);
    ctx.lineTo(width - padding, y);
    ctx.stroke();
    ctx.setLineDash([]);

    // 绘制价格标签
    ctx.fillStyle = '#ffd700';
    ctx.fillRect(width - 80, y - 10, 75, 20);
    ctx.fillStyle = '#000';
    ctx.font = 'bold 12px Arial';
    ctx.textAlign = 'center';
    ctx.fillText('$' + currentPrice.toFixed(2), width - 42.5, y + 4);
}
                color: '#2B2B43',
            },
            horzLines: {
                color: '#2B2B43',
            },
        },
        crosshair: {
            mode: LightweightCharts.CrosshairMode.Normal,
        },
        rightPriceScale: {
            borderColor: '#485c7b',
        },
        timeScale: {
            borderColor: '#485c7b',
            timeVisible: true,
            secondsVisible: false,
        },
        handleScroll: {
            mouseWheel: true,
            pressedMouseMove: true,
        },
        handleScale: {
            axisPressedMouseMove: true,
            mouseWheel: true,
            pinch: true,
        },
    };

    KLINE_CONFIG.chart = LightweightCharts.createChart(KLINE_CONFIG.container, chartOptions);

    // 创建K线系列
    KLINE_CONFIG.candlestickSeries = KLINE_CONFIG.chart.addCandlestickSeries({
        upColor: '#26a69a',
        downColor: '#ef5350',
        borderVisible: false,
        wickUpColor: '#26a69a',
        wickDownColor: '#ef5350',
    });

    // 创建成交量系列（如果启用）
    if (KLINE_CONFIG.showVolume) {
        KLINE_CONFIG.volumeSeries = KLINE_CONFIG.chart.addHistogramSeries({
            color: '#26a69a',
            priceFormat: {
                type: 'volume',
            },
            priceScaleId: 'volume',
            scaleMargins: {
                top: 0.8,
                bottom: 0,
            },
        });
    }
}

// 加载K线数据
async function loadKlineData(symbol, timeframe) {
    try {
        console.log(`📊 加载K线数据: ${symbol} ${timeframe}`);

        const config = TIMEFRAMES[timeframe];
        if (!config) {
            throw new Error(`不支持的时间框架: ${timeframe}`);
        }

        // 从后端API获取K线数据
        const response = await fetch(`/api/binance/klines?symbol=${symbol}&interval=${config.interval}&limit=${config.limit}`);
        const data = await response.json();

        if (!data.success) {
            // 如果API失败，使用模拟数据
            console.log('⚠️ API数据获取失败，使用模拟数据');
            const mockData = generateMockKlineData(symbol);
            drawKlineChart(mockData);
            return;
        }

        const klineData = data.data;

        // 转换数据格式
        const candleData = klineData.map(kline => ({
            time: Math.floor(kline[0] / 1000), // 转换为秒
            open: parseFloat(kline[1]),
            high: parseFloat(kline[2]),
            low: parseFloat(kline[3]),
            close: parseFloat(kline[4]),
            volume: parseFloat(kline[5])
        }));

        // 保存数据供重绘使用
        KLINE_CONFIG.lastData = candleData;

        // 绘制K线图
        drawKlineChart(candleData);

        console.log(`✅ K线数据加载完成: ${candleData.length} 根K线`);
        return candleData;

    } catch (error) {
        console.error('❌ K线数据加载失败:', error);
        return null;
    }
}

// 检测并绘制支撑阻力位
async function detectAndDrawSupportResistance(candleData, symbol) {
    try {
        // 清除现有的支撑阻力线
        clearSupportResistanceLines();

        // 使用telegram-bot.js中的检测算法
        if (typeof window.detectSupportResistanceLevels === 'function') {
            const levels = window.detectSupportResistanceLevels(symbol, candleData, KLINE_CONFIG.currentTimeframe);
            
            if (levels) {
                // 绘制支撑位（绿色虚线）
                levels.support.forEach((level, index) => {
                    const line = KLINE_CONFIG.chart.addPriceLine({
                        price: level.price,
                        color: '#26a69a',
                        lineWidth: 2,
                        lineStyle: LightweightCharts.LineStyle.Dashed,
                        axisLabelVisible: true,
                        title: `支撑${index + 1}: ${formatPrice(level.price)}`
                    });
                    KLINE_CONFIG.supportLines.push(line);
                });

                // 绘制阻力位（红色虚线）
                levels.resistance.forEach((level, index) => {
                    const line = KLINE_CONFIG.chart.addPriceLine({
                        price: level.price,
                        color: '#ef5350',
                        lineWidth: 2,
                        lineStyle: LightweightCharts.LineStyle.Dashed,
                        axisLabelVisible: true,
                        title: `阻力${index + 1}: ${formatPrice(level.price)}`
                    });
                    KLINE_CONFIG.resistanceLines.push(line);
                });

                console.log(`✅ 支撑阻力位绘制完成: ${levels.support.length}个支撑位, ${levels.resistance.length}个阻力位`);
            }
        }
    } catch (error) {
        console.error('❌ 支撑阻力位检测失败:', error);
    }
}

// 清除支撑阻力线
function clearSupportResistanceLines() {
    KLINE_CONFIG.supportLines.forEach(line => {
        KLINE_CONFIG.candlestickSeries.removePriceLine(line);
    });
    KLINE_CONFIG.resistanceLines.forEach(line => {
        KLINE_CONFIG.candlestickSeries.removePriceLine(line);
    });
    
    KLINE_CONFIG.supportLines = [];
    KLINE_CONFIG.resistanceLines = [];
}

// 设置自动调整大小
function setupAutoResize() {
    const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
            const { width, height } = entry.contentRect;
            KLINE_CONFIG.chart.applyOptions({
                width: width,
                height: height || 400
            });
        }
    });

    resizeObserver.observe(KLINE_CONFIG.container);
}

// 启动实时数据更新
function startRealTimeUpdates() {
    // 每30秒更新一次数据
    setInterval(async () => {
        if (KLINE_CONFIG.currentSymbol && KLINE_CONFIG.currentTimeframe) {
            await updateLatestKline();
        }
    }, 30000);
}

// 更新最新K线数据
async function updateLatestKline() {
    try {
        // 获取最新的K线数据
        const response = await fetch(`/api/binance/klines?symbol=${KLINE_CONFIG.currentSymbol}&interval=${TIMEFRAMES[KLINE_CONFIG.currentTimeframe].interval}&limit=1`);
        const data = await response.json();

        if (data.success && data.data.length > 0) {
            const latestKline = data.data[0];
            const candleData = {
                time: Math.floor(latestKline[0] / 1000),
                open: parseFloat(latestKline[1]),
                high: parseFloat(latestKline[2]),
                low: parseFloat(latestKline[3]),
                close: parseFloat(latestKline[4])
            };

            // 更新图表
            KLINE_CONFIG.candlestickSeries.update(candleData);

            if (KLINE_CONFIG.volumeSeries) {
                const volumeData = {
                    time: Math.floor(latestKline[0] / 1000),
                    value: parseFloat(latestKline[5]),
                    color: parseFloat(latestKline[4]) >= parseFloat(latestKline[1]) ? '#26a69a' : '#ef5350'
                };
                KLINE_CONFIG.volumeSeries.update(volumeData);
            }
        }
    } catch (error) {
        console.error('❌ 实时K线更新失败:', error);
    }
}

// 切换交易对
async function switchSymbol(symbol) {
    KLINE_CONFIG.currentSymbol = symbol;
    await loadKlineData(symbol, KLINE_CONFIG.currentTimeframe);
}

// 切换时间框架
async function switchTimeframe(timeframe) {
    if (!TIMEFRAMES[timeframe]) {
        console.error('❌ 不支持的时间框架:', timeframe);
        return;
    }
    
    KLINE_CONFIG.currentTimeframe = timeframe;
    await loadKlineData(KLINE_CONFIG.currentSymbol, timeframe);
}

// 生成模拟K线数据
function generateMockKlineData(symbol) {
    const basePrice = symbol === 'BTCUSDT' ? 100000 :
                     symbol === 'ETHUSDT' ? 3500 :
                     symbol === 'BNBUSDT' ? 650 : 50000;

    const data = [];
    let currentPrice = basePrice;

    for (let i = 0; i < 50; i++) {
        const change = (Math.random() - 0.5) * basePrice * 0.02; // 2%的随机变化
        const open = currentPrice;
        const close = currentPrice + change;
        const high = Math.max(open, close) + Math.random() * basePrice * 0.01;
        const low = Math.min(open, close) - Math.random() * basePrice * 0.01;

        data.push({
            time: Date.now() - (50 - i) * 60000, // 每分钟一根K线
            open: open,
            high: high,
            low: low,
            close: close,
            volume: Math.random() * 1000
        });

        currentPrice = close;
    }

    return data;
}

// 设置自动调整大小
function setupAutoResize() {
    const resizeObserver = new ResizeObserver(() => {
        if (KLINE_CONFIG.canvas) {
            resizeCanvas();
            // 重新绘制
            if (KLINE_CONFIG.lastData) {
                drawKlineChart(KLINE_CONFIG.lastData);
            }
        }
    });

    if (KLINE_CONFIG.container) {
        resizeObserver.observe(KLINE_CONFIG.container);
    }
}

// 启动实时数据更新
function startRealTimeUpdates() {
    // 每30秒更新一次数据
    setInterval(async () => {
        if (KLINE_CONFIG.currentSymbol) {
            await loadKlineData(KLINE_CONFIG.currentSymbol, KLINE_CONFIG.currentTimeframe);
        }
    }, 30000);
}

// 测试K线图绘制
function testKlineChart() {
    console.log('🧪 测试K线图绘制...');
    const container = document.getElementById('klineChartContainer');
    if (container) {
        const mockData = generateMockKlineData('BTCUSDT');
        console.log('📊 生成模拟数据:', mockData.length, '条');

        // 直接初始化
        initializeAdvancedKlineChart('klineChartContainer', 'BTCUSDT');
    } else {
        console.error('❌ K线图容器未找到');
    }
}

// 导出函数
window.initializeAdvancedKlineChart = initializeAdvancedKlineChart;
window.switchSymbol = switchSymbol;
window.switchTimeframe = switchTimeframe;
window.KLINE_CONFIG = KLINE_CONFIG;
window.TIMEFRAMES = TIMEFRAMES;
window.generateMockKlineData = generateMockKlineData;
window.testKlineChart = testKlineChart;

console.log('📈 高级K线图模块已加载');
