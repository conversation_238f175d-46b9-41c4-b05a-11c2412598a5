// 断金ใ现货'杠杆自动化交易系统 - 高级K线图模块
// 功能：TradingView风格的专业K线图，支撑阻力位标注，多时间框架
// 特性：自适应大小，实时数据，技术指标叠加

// K线图配置
const KLINE_CONFIG = {
    container: null,
    chart: null,
    candlestickSeries: null,
    volumeSeries: null,
    supportLines: [],
    resistanceLines: [],
    currentSymbol: 'BTCUSDT',
    currentTimeframe: '1h',
    autoResize: true,
    showVolume: true,
    showIndicators: true
};

// 时间框架配置
const TIMEFRAMES = {
    '1m': { label: '1分钟', interval: '1m', limit: 100 },
    '5m': { label: '5分钟', interval: '5m', limit: 100 },
    '15m': { label: '15分钟', interval: '15m', limit: 100 },
    '1h': { label: '1小时', interval: '1h', limit: 100 },
    '4h': { label: '4小时', interval: '4h', limit: 100 },
    '1d': { label: '1天', interval: '1d', limit: 100 },
    '1w': { label: '1周', interval: '1w', limit: 100 }
};

// 初始化高级K线图
async function initializeAdvancedKlineChart(containerId, symbol = 'BTCUSDT') {
    try {
        console.log('🚀 初始化高级K线图...');
        
        // 检查TradingView Lightweight Charts是否可用
        if (typeof LightweightCharts === 'undefined') {
            console.log('📦 加载TradingView Lightweight Charts...');
            await loadTradingViewCharts();
        }

        const container = document.getElementById(containerId);
        if (!container) {
            throw new Error(`容器 ${containerId} 不存在`);
        }

        KLINE_CONFIG.container = container;
        KLINE_CONFIG.currentSymbol = symbol;

        // 创建图表
        createChart();
        
        // 加载初始数据
        await loadKlineData(symbol, KLINE_CONFIG.currentTimeframe);
        
        // 设置自动调整大小
        if (KLINE_CONFIG.autoResize) {
            setupAutoResize();
        }

        // 启动实时数据更新
        startRealTimeUpdates();

        console.log('✅ 高级K线图初始化完成');
        return true;

    } catch (error) {
        console.error('❌ 高级K线图初始化失败:', error);
        return false;
    }
}

// 动态加载TradingView Charts库
async function loadTradingViewCharts() {
    return new Promise((resolve, reject) => {
        const script = document.createElement('script');
        script.src = 'https://unpkg.com/lightweight-charts/dist/lightweight-charts.standalone.production.js';
        script.onload = () => {
            console.log('✅ TradingView Charts库加载成功');
            resolve();
        };
        script.onerror = () => {
            console.error('❌ TradingView Charts库加载失败');
            reject(new Error('TradingView Charts库加载失败'));
        };
        document.head.appendChild(script);
    });
}

// 创建图表
function createChart() {
    const chartOptions = {
        width: KLINE_CONFIG.container.clientWidth,
        height: KLINE_CONFIG.container.clientHeight || 400,
        layout: {
            backgroundColor: '#1a1a1a',
            textColor: '#d1d4dc',
        },
        grid: {
            vertLines: {
                color: '#2B2B43',
            },
            horzLines: {
                color: '#2B2B43',
            },
        },
        crosshair: {
            mode: LightweightCharts.CrosshairMode.Normal,
        },
        rightPriceScale: {
            borderColor: '#485c7b',
        },
        timeScale: {
            borderColor: '#485c7b',
            timeVisible: true,
            secondsVisible: false,
        },
        handleScroll: {
            mouseWheel: true,
            pressedMouseMove: true,
        },
        handleScale: {
            axisPressedMouseMove: true,
            mouseWheel: true,
            pinch: true,
        },
    };

    KLINE_CONFIG.chart = LightweightCharts.createChart(KLINE_CONFIG.container, chartOptions);

    // 创建K线系列
    KLINE_CONFIG.candlestickSeries = KLINE_CONFIG.chart.addCandlestickSeries({
        upColor: '#26a69a',
        downColor: '#ef5350',
        borderVisible: false,
        wickUpColor: '#26a69a',
        wickDownColor: '#ef5350',
    });

    // 创建成交量系列（如果启用）
    if (KLINE_CONFIG.showVolume) {
        KLINE_CONFIG.volumeSeries = KLINE_CONFIG.chart.addHistogramSeries({
            color: '#26a69a',
            priceFormat: {
                type: 'volume',
            },
            priceScaleId: 'volume',
            scaleMargins: {
                top: 0.8,
                bottom: 0,
            },
        });
    }
}

// 加载K线数据
async function loadKlineData(symbol, timeframe) {
    try {
        console.log(`📊 加载K线数据: ${symbol} ${timeframe}`);
        
        const config = TIMEFRAMES[timeframe];
        if (!config) {
            throw new Error(`不支持的时间框架: ${timeframe}`);
        }

        // 从后端API获取K线数据
        const response = await fetch(`/api/binance/klines?symbol=${symbol}&interval=${config.interval}&limit=${config.limit}`);
        const data = await response.json();

        if (!data.success) {
            throw new Error(data.error || 'K线数据获取失败');
        }

        const klineData = data.data;
        
        // 转换数据格式
        const candleData = klineData.map(kline => ({
            time: Math.floor(kline[0] / 1000), // 转换为秒
            open: parseFloat(kline[1]),
            high: parseFloat(kline[2]),
            low: parseFloat(kline[3]),
            close: parseFloat(kline[4])
        }));

        const volumeData = klineData.map(kline => ({
            time: Math.floor(kline[0] / 1000),
            value: parseFloat(kline[5]),
            color: parseFloat(kline[4]) >= parseFloat(kline[1]) ? '#26a69a' : '#ef5350'
        }));

        // 更新图表数据
        KLINE_CONFIG.candlestickSeries.setData(candleData);
        
        if (KLINE_CONFIG.volumeSeries) {
            KLINE_CONFIG.volumeSeries.setData(volumeData);
        }

        // 检测并绘制支撑阻力位
        await detectAndDrawSupportResistance(candleData, symbol);

        console.log(`✅ K线数据加载完成: ${candleData.length} 根K线`);
        return candleData;

    } catch (error) {
        console.error('❌ K线数据加载失败:', error);
        return null;
    }
}

// 检测并绘制支撑阻力位
async function detectAndDrawSupportResistance(candleData, symbol) {
    try {
        // 清除现有的支撑阻力线
        clearSupportResistanceLines();

        // 使用telegram-bot.js中的检测算法
        if (typeof window.detectSupportResistanceLevels === 'function') {
            const levels = window.detectSupportResistanceLevels(symbol, candleData, KLINE_CONFIG.currentTimeframe);
            
            if (levels) {
                // 绘制支撑位（绿色虚线）
                levels.support.forEach((level, index) => {
                    const line = KLINE_CONFIG.chart.addPriceLine({
                        price: level.price,
                        color: '#26a69a',
                        lineWidth: 2,
                        lineStyle: LightweightCharts.LineStyle.Dashed,
                        axisLabelVisible: true,
                        title: `支撑${index + 1}: ${formatPrice(level.price)}`
                    });
                    KLINE_CONFIG.supportLines.push(line);
                });

                // 绘制阻力位（红色虚线）
                levels.resistance.forEach((level, index) => {
                    const line = KLINE_CONFIG.chart.addPriceLine({
                        price: level.price,
                        color: '#ef5350',
                        lineWidth: 2,
                        lineStyle: LightweightCharts.LineStyle.Dashed,
                        axisLabelVisible: true,
                        title: `阻力${index + 1}: ${formatPrice(level.price)}`
                    });
                    KLINE_CONFIG.resistanceLines.push(line);
                });

                console.log(`✅ 支撑阻力位绘制完成: ${levels.support.length}个支撑位, ${levels.resistance.length}个阻力位`);
            }
        }
    } catch (error) {
        console.error('❌ 支撑阻力位检测失败:', error);
    }
}

// 清除支撑阻力线
function clearSupportResistanceLines() {
    KLINE_CONFIG.supportLines.forEach(line => {
        KLINE_CONFIG.candlestickSeries.removePriceLine(line);
    });
    KLINE_CONFIG.resistanceLines.forEach(line => {
        KLINE_CONFIG.candlestickSeries.removePriceLine(line);
    });
    
    KLINE_CONFIG.supportLines = [];
    KLINE_CONFIG.resistanceLines = [];
}

// 设置自动调整大小
function setupAutoResize() {
    const resizeObserver = new ResizeObserver(entries => {
        for (let entry of entries) {
            const { width, height } = entry.contentRect;
            KLINE_CONFIG.chart.applyOptions({
                width: width,
                height: height || 400
            });
        }
    });

    resizeObserver.observe(KLINE_CONFIG.container);
}

// 启动实时数据更新
function startRealTimeUpdates() {
    // 每30秒更新一次数据
    setInterval(async () => {
        if (KLINE_CONFIG.currentSymbol && KLINE_CONFIG.currentTimeframe) {
            await updateLatestKline();
        }
    }, 30000);
}

// 更新最新K线数据
async function updateLatestKline() {
    try {
        // 获取最新的K线数据
        const response = await fetch(`/api/binance/klines?symbol=${KLINE_CONFIG.currentSymbol}&interval=${TIMEFRAMES[KLINE_CONFIG.currentTimeframe].interval}&limit=1`);
        const data = await response.json();

        if (data.success && data.data.length > 0) {
            const latestKline = data.data[0];
            const candleData = {
                time: Math.floor(latestKline[0] / 1000),
                open: parseFloat(latestKline[1]),
                high: parseFloat(latestKline[2]),
                low: parseFloat(latestKline[3]),
                close: parseFloat(latestKline[4])
            };

            // 更新图表
            KLINE_CONFIG.candlestickSeries.update(candleData);

            if (KLINE_CONFIG.volumeSeries) {
                const volumeData = {
                    time: Math.floor(latestKline[0] / 1000),
                    value: parseFloat(latestKline[5]),
                    color: parseFloat(latestKline[4]) >= parseFloat(latestKline[1]) ? '#26a69a' : '#ef5350'
                };
                KLINE_CONFIG.volumeSeries.update(volumeData);
            }
        }
    } catch (error) {
        console.error('❌ 实时K线更新失败:', error);
    }
}

// 切换交易对
async function switchSymbol(symbol) {
    KLINE_CONFIG.currentSymbol = symbol;
    await loadKlineData(symbol, KLINE_CONFIG.currentTimeframe);
}

// 切换时间框架
async function switchTimeframe(timeframe) {
    if (!TIMEFRAMES[timeframe]) {
        console.error('❌ 不支持的时间框架:', timeframe);
        return;
    }
    
    KLINE_CONFIG.currentTimeframe = timeframe;
    await loadKlineData(KLINE_CONFIG.currentSymbol, timeframe);
}

// 导出函数
window.initializeAdvancedKlineChart = initializeAdvancedKlineChart;
window.switchSymbol = switchSymbol;
window.switchTimeframe = switchTimeframe;
window.KLINE_CONFIG = KLINE_CONFIG;
window.TIMEFRAMES = TIMEFRAMES;

console.log('📈 高级K线图模块已加载');
