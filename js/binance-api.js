// 断金ใ现货'杠杆自动化交易系统 - 币安API集成模块
// 功能：币安实盘API集成，包括价格获取、账户信息、交易执行等
// 安全性：API密钥通过后端代理，前端不直接暴露

// API状态管理 - 全局状态
window.apiStatus = {
    connected: false,           // API连接状态
    lastUpdate: null,          // 最后更新时间
    errorCount: 0,             // 错误计数
    rateLimitRemaining: 1200,  // 剩余请求次数
    websocketConnected: false   // WebSocket连接状态
};

// 全局账户余额 - 现货和杠杆分别管理
window.spotAccountBalance = null;      // 现货账户余额
window.marginAccountBalance = null;    // 杠杆全仓账户余额
window.accountBalance = null;          // 兼容旧代码

// WebSocket连接管理
let priceWebSocket = null;
let userDataWebSocket = null;

// 初始化币安API连接
async function initializeBinanceAPI() {
    console.log('🔗 初始化币安API连接...');

    try {
        // 测试API连接
        const serverTime = await getBinanceServerTime();
        if (serverTime) {
            window.apiStatus.connected = true;
            window.apiStatus.lastUpdate = new Date();
            console.log('✅ 币安API连接成功');

            // 发送TG连接成功通知
            if (window.sendBinanceConnectionStatus) {
                window.sendBinanceConnectionStatus(true, 'API密钥验证成功');
            }

            // 获取现货账户信息
            const spotSuccess = await fetchSpotAccountInfo();

            // 获取杠杆账户信息
            const marginSuccess = await fetchMarginAccountInfo();

            // 暂时禁用WebSocket连接，避免CORS错误
            // await initializePriceWebSocket();

            // 获取初始数据
            await fetchInitialData();

            // 更新UI显示
            updateConnectionStatus();

            // 强制更新余额显示
            if (window.updateBalanceDisplay) {
                window.updateBalanceDisplay();
            }

            console.log('✅ 币安API初始化完成');
            console.log('💰 现货账户余额:', window.spotAccountBalance);
            console.log('⚡ 杠杆账户余额:', window.marginAccountBalance);

            return true;
        }
    } catch (error) {
        console.error('❌ 币安API连接失败:', error);
        window.apiStatus.connected = false;
        window.apiStatus.errorCount++;

        // 发送TG连接失败通知
        if (window.sendBinanceConnectionStatus) {
            window.sendBinanceConnectionStatus(false, error.message);
        }

        updateConnectionStatus();
    }

    return false;
}

// 更新连接状态显示
function updateConnectionStatus() {
    const binanceBtn = document.querySelector('.control-btn[onclick*="binance"]');
    if (binanceBtn) {
        if (window.apiStatus.connected) {
            binanceBtn.textContent = '币安API ✅';
            binanceBtn.style.backgroundColor = '#10b981';
        } else {
            binanceBtn.textContent = '币安API ❌';
            binanceBtn.style.backgroundColor = '#ef4444';
        }
    }

    // 更新余额显示
    if (window.updateBalanceDisplay) {
        window.updateBalanceDisplay();
    }
}

// 获取币安服务器时间 - 测试API连接
async function getBinanceServerTime() {
    try {
        const response = await fetch('http://localhost:3001/api/binance/time'); // 通过后端代理
        const data = await response.json();

        if (data.success && data.data && data.data.serverTime) {
            // 安全地处理服务器时间
            const serverTime = data.data.serverTime;
            console.log('⏰ 币安服务器时间戳:', serverTime);

            // 验证时间戳是否有效
            if (typeof serverTime === 'number' && serverTime > 0) {
                const serverDate = new Date(serverTime);
                if (!isNaN(serverDate.getTime())) {
                    console.log('⏰ 币安服务器时间:', serverDate.toLocaleString('zh-CN'));
                    return serverTime;
                } else {
                    console.warn('⚠️ 服务器时间戳无效:', serverTime);
                    return Date.now(); // 使用本地时间作为备用
                }
            } else {
                console.warn('⚠️ 服务器时间戳格式错误:', serverTime);
                return Date.now(); // 使用本地时间作为备用
            }
        } else {
            throw new Error(data.error || '获取服务器时间失败');
        }
    } catch (error) {
        console.error('❌ 获取币安服务器时间失败:', error);
        console.error('❌ 错误详情:', error.message);
        return null;
    }
}

// 获取现货账户信息
async function fetchSpotAccountInfo() {
    try {
        console.log('📡 正在获取现货账户信息...');
        const response = await fetch('http://localhost:3001/api/binance/account'); // 通过后端代理
        const data = await response.json();

        console.log('📊 现货账户API响应数据:', data);

        if (data.success) {
            // 处理币安API返回的数据结构
            const accountData = data.data || data;
            const balances = accountData.balances || [];

            // 计算总余额和可用余额
            let totalBalance = 0;
            let availableBalance = 0;
            let frozenBalance = 0;

            // 处理所有有余额的资产
            balances.forEach(balance => {
                const free = parseFloat(balance.free || 0);
                const locked = parseFloat(balance.locked || 0);

                if (free > 0 || locked > 0) {
                    console.log(`💰 现货资产: ${balance.asset}, 可用: ${free}, 冻结: ${locked}`);

                    // 主要计算USDT余额
                    if (balance.asset === 'USDT') {
                        availableBalance += free;
                        frozenBalance += locked;
                        totalBalance += free + locked;
                    }
                    // 其他资产也计入总余额（简化处理，按1:1计算）
                    else {
                        totalBalance += free + locked;
                    }
                }
            });

            // 更新现货账户余额
            window.spotAccountBalance = {
                totalBalance: totalBalance,
                availableBalance: availableBalance,
                frozenBalance: frozenBalance,
                dailyPnL: 0, // 需要额外计算
                dailyChangePercent: 0
            };

            // 兼容旧代码
            window.accountBalance = window.spotAccountBalance;

            console.log('✅ 现货账户信息获取成功:', window.spotAccountBalance);
            return true;
        } else {
            throw new Error(data.error || '获取现货账户信息失败');
        }
    } catch (error) {
        console.error('❌ 获取现货账户信息失败:', error);
        console.error('错误详情:', error);
        window.spotAccountBalance = null;
        return false;
    }
}

// 获取杠杆全仓账户信息
async function fetchMarginAccountInfo() {
    try {
        console.log('📡 正在获取杠杆账户信息...');
        const response = await fetch('http://localhost:3001/api/binance/margin/account'); // 通过后端代理
        const data = await response.json();

        console.log('📊 杠杆账户API响应数据:', data);

        if (data.success) {
            // 处理币安杠杆API返回的数据结构
            const marginData = data.data || data;

            // 计算总资产和负债
            let totalAsset = 0;
            let totalLiability = 0;

            // 从userAssets数组中计算总资产
            if (marginData.userAssets && Array.isArray(marginData.userAssets)) {
                marginData.userAssets.forEach(asset => {
                    const free = parseFloat(asset.free || 0);
                    const locked = parseFloat(asset.locked || 0);
                    const borrowed = parseFloat(asset.borrowed || 0);
                    const interest = parseFloat(asset.interest || 0);

                    // 计算净资产（正数为资产，负数为负债）
                    const netAsset = parseFloat(asset.netAsset || 0);

                    if (netAsset > 0) {
                        // 对于USDT，直接使用净资产值
                        if (asset.asset === 'USDT') {
                            totalAsset += netAsset;
                        } else {
                            // 对于其他资产，也计入总资产（简化处理）
                            totalAsset += netAsset;
                        }
                    } else if (netAsset < 0) {
                        // 负债
                        totalLiability += Math.abs(netAsset);
                    }

                    // 记录有余额的资产
                    if (free > 0 || locked > 0 || borrowed > 0) {
                        console.log(`⚡ 杠杆资产: ${asset.asset}, 可用: ${free}, 冻结: ${locked}, 借贷: ${borrowed}, 净资产: ${netAsset}`);
                    }
                });
            }

            // 如果没有从userAssets计算出资产，尝试使用BTC价值转换
            if (totalAsset === 0 && marginData.totalNetAssetOfBtc) {
                // 这里需要BTC价格来转换，暂时使用一个估算值
                const btcPrice = 100000; // 估算BTC价格，实际应该从价格API获取
                totalAsset = parseFloat(marginData.totalNetAssetOfBtc) * btcPrice;
                console.log(`⚡ 使用BTC价值计算: ${marginData.totalNetAssetOfBtc} BTC ≈ $${totalAsset.toFixed(2)}`);
            }

            const netAsset = totalAsset - totalLiability;
            const marginLevel = parseFloat(marginData.marginLevel || 999);

            // 更新杠杆账户余额
            window.marginAccountBalance = {
                totalAsset: totalAsset,
                totalLiability: totalLiability,
                netAsset: netAsset,
                marginLevel: marginLevel,
                dailyPnL: 0, // 需要额外计算
                dailyChangePercent: 0
            };

            console.log('✅ 杠杆账户信息获取成功:', window.marginAccountBalance);
            return true;
        } else {
            console.warn('⚠️ 杠杆账户API调用失败，可能未开通杠杆交易');
            // 设置默认值
            window.marginAccountBalance = {
                totalAsset: 0,
                totalLiability: 0,
                netAsset: 0,
                marginLevel: 999,
                dailyPnL: 0,
                dailyChangePercent: 0
            };
            return true; // 不阻断流程
        }
    } catch (error) {
        console.error('❌ 获取杠杆账户信息失败:', error);
        console.error('错误详情:', error);
        // 设置默认值，不阻断流程
        window.marginAccountBalance = {
            totalAsset: 0,
            totalLiability: 0,
            netAsset: 0,
            marginLevel: 999,
            dailyPnL: 0,
            dailyChangePercent: 0
        };
        return true;
    }
}

// 获取24小时价格变动数据
async function fetch24hrTicker() {
    try {
        const response = await fetch('http://localhost:3001/api/binance/ticker/24hr');
        const data = await response.json();
        
        if (data.success) {
            return data.data;
        } else {
            throw new Error(data.error || '获取价格数据失败');
        }
    } catch (error) {
        console.error('❌ 获取24小时价格数据失败:', error);
        return null;
    }
}

// 获取账户信息
async function getAccountInfo() {
    try {
        const response = await fetch('http://localhost:3001/api/binance/account');
        const data = await response.json();
        
        if (data.success) {
            return data.data;
        } else {
            throw new Error(data.error || '获取账户信息失败');
        }
    } catch (error) {
        console.error('❌ 获取账户信息失败:', error);
        return null;
    }
}

// 获取账户余额
async function getAccountBalance() {
    try {
        const accountInfo = await getAccountInfo();
        if (!accountInfo) return null;
        
        // 计算总资产价值 (USDT)
        let totalBalance = 0;
        let availableBalance = 0;
        let frozenBalance = 0;
        
        // 处理余额数据
        const balances = accountInfo.balances.filter(balance => 
            parseFloat(balance.free) > 0 || parseFloat(balance.locked) > 0
        );
        
        // 获取USDT余额
        const usdtBalance = balances.find(b => b.asset === 'USDT');
        if (usdtBalance) {
            availableBalance = parseFloat(usdtBalance.free);
            frozenBalance = parseFloat(usdtBalance.locked);
        }
        
        // 计算其他资产的USDT价值
        for (const balance of balances) {
            if (balance.asset === 'USDT') continue;
            
            const symbol = balance.asset + 'USDT';
            const crypto = cryptos.find(c => c.symbol === symbol);
            if (crypto && crypto.price > 0) {
                const assetValue = (parseFloat(balance.free) + parseFloat(balance.locked)) * crypto.price;
                totalBalance += assetValue;
            }
        }
        
        totalBalance += availableBalance + frozenBalance;
        
        // 更新全局账户余额
        accountBalance.totalBalance = totalBalance;
        accountBalance.availableBalance = availableBalance;
        accountBalance.frozenBalance = frozenBalance;
        
        console.log('💰 账户余额更新:', {
            total: totalBalance.toFixed(2),
            available: availableBalance.toFixed(2),
            frozen: frozenBalance.toFixed(2)
        });
        
        return accountBalance;
        
    } catch (error) {
        console.error('❌ 获取账户余额失败:', error);
        return null;
    }
}

// 初始化价格WebSocket连接
async function initializePriceWebSocket() {
    try {
        // 构建WebSocket URL - 订阅所有支持的交易对
        const streams = SUPPORTED_SYMBOLS.map(symbol => 
            `${symbol.toLowerCase()}@ticker`
        ).join('/');
        
        const wsUrl = `${BINANCE_CONFIG.WS_URL}stream?streams=${streams}`;
        
        priceWebSocket = new WebSocket(wsUrl);
        
        priceWebSocket.onopen = function() {
            console.log('🔗 价格WebSocket连接已建立');
            apiStatus.websocketConnected = true;
        };
        
        priceWebSocket.onmessage = function(event) {
            try {
                const data = JSON.parse(event.data);
                if (data.stream && data.data) {
                    updateCryptoPriceFromWebSocket(data.data);
                }
            } catch (error) {
                console.error('❌ WebSocket数据解析失败:', error);
            }
        };
        
        priceWebSocket.onclose = function() {
            console.log('🔌 价格WebSocket连接已断开');
            apiStatus.websocketConnected = false;
            
            // 5秒后尝试重连
            setTimeout(() => {
                if (apiStatus.connected) {
                    console.log('🔄 尝试重连价格WebSocket...');
                    initializePriceWebSocket();
                }
            }, 5000);
        };
        
        priceWebSocket.onerror = function(error) {
            console.error('❌ 价格WebSocket连接错误:', error);
            apiStatus.websocketConnected = false;
        };
        
    } catch (error) {
        console.error('❌ 初始化价格WebSocket失败:', error);
    }
}

// 从WebSocket更新加密货币价格
function updateCryptoPriceFromWebSocket(tickerData) {
    const symbol = tickerData.s; // 交易对符号
    const price = parseFloat(tickerData.c); // 当前价格
    const change = parseFloat(tickerData.P); // 24小时价格变化百分比
    const volume = parseFloat(tickerData.v); // 24小时交易量
    
    // 更新cryptos数组中的对应数据
    const cryptoIndex = cryptos.findIndex(crypto => crypto.symbol === symbol);
    if (cryptoIndex !== -1) {
        cryptos[cryptoIndex].price = price;
        cryptos[cryptoIndex].change = change;
        cryptos[cryptoIndex].volume = volume;
        
        // 更新UI显示
        if (window.updateCryptoGrid) {
            window.updateCryptoGrid();
        }
        
        // 更新实时指示器
        if (window.updateRealTimeIndicator) {
            window.updateRealTimeIndicator();
        }
    }
}

// 获取初始数据
async function fetchInitialData() {
    console.log('📊 获取初始数据...');
    
    try {
        // 获取24小时价格数据
        const tickerData = await fetch24hrTicker();
        if (tickerData) {
            updateCryptosFromTicker(tickerData);
        }
        
        // 获取账户余额
        await getAccountBalance();
        
        // 获取持仓信息
        await updatePositions();
        
        console.log('✅ 初始数据获取完成');
        
    } catch (error) {
        console.error('❌ 获取初始数据失败:', error);
    }
}

// 从Ticker数据更新加密货币信息
function updateCryptosFromTicker(tickerData) {
    tickerData.forEach(ticker => {
        const cryptoIndex = cryptos.findIndex(crypto => crypto.symbol === ticker.symbol);
        if (cryptoIndex !== -1) {
            cryptos[cryptoIndex].price = parseFloat(ticker.lastPrice);
            cryptos[cryptoIndex].change = parseFloat(ticker.priceChangePercent);
            cryptos[cryptoIndex].volume = parseFloat(ticker.volume);
        }
    });
    
    console.log('📈 加密货币价格数据已更新');
    
    // 更新UI
    if (window.updateCryptoGrid) {
        window.updateCryptoGrid();
    }
}

// 更新持仓信息
async function updatePositions() {
    try {
        const accountInfo = await getAccountInfo();
        if (!accountInfo) return;
        
        // 清空现有持仓
        positions.length = 0;
        
        // 处理有余额的资产
        const balances = accountInfo.balances.filter(balance => 
            parseFloat(balance.free) > 0 || parseFloat(balance.locked) > 0
        );
        
        let totalValue = 0;
        const assetValues = [];
        
        // 计算每个资产的USDT价值
        for (const balance of balances) {
            const totalAmount = parseFloat(balance.free) + parseFloat(balance.locked);
            if (totalAmount <= 0) continue;
            
            let usdtValue = 0;
            
            if (balance.asset === 'USDT') {
                usdtValue = totalAmount;
            } else {
                const symbol = balance.asset + 'USDT';
                const crypto = cryptos.find(c => c.symbol === symbol);
                if (crypto && crypto.price > 0) {
                    usdtValue = totalAmount * crypto.price;
                }
            }
            
            if (usdtValue > 1) { // 只显示价值超过1 USDT的资产
                assetValues.push({
                    symbol: balance.asset,
                    value: usdtValue,
                    amount: totalAmount
                });
                totalValue += usdtValue;
            }
        }
        
        // 按价值排序并计算百分比
        assetValues.sort((a, b) => b.value - a.value);
        
        // 只显示前6个最大持仓
        assetValues.slice(0, 6).forEach(asset => {
            const percentage = (asset.value / totalValue * 100);
            positions.push({
                symbol: asset.symbol,
                percentage: percentage,
                value: `$${asset.value.toFixed(2)}`,
                amount: asset.amount.toFixed(6)
            });
        });
        
        console.log('📊 持仓信息已更新:', positions);
        
        // 更新UI
        if (window.updatePositionChart) {
            window.updatePositionChart();
        }
        
    } catch (error) {
        console.error('❌ 更新持仓信息失败:', error);
    }
}

// 导出函数到全局作用域
window.BINANCE_CONFIG = BINANCE_CONFIG;
window.TELEGRAM_CONFIG = TELEGRAM_CONFIG;
window.initializeBinanceAPI = initializeBinanceAPI;
window.getBinanceServerTime = getBinanceServerTime;
window.fetchSpotAccountInfo = fetchSpotAccountInfo;
window.fetchMarginAccountInfo = fetchMarginAccountInfo;
window.fetchAccountInfo = fetchSpotAccountInfo; // 兼容旧代码
window.getAccountInfo = getAccountInfo;
window.getAccountBalance = getAccountBalance;
window.updatePositions = updatePositions;
window.apiStatus = apiStatus;

console.log('🔗 币安API集成模块已加载');
