// 断金ใ现货'杠杆自动化交易系统 - UI更新模块
// 功能：界面更新、响应式布局、动态内容生成

// 初始化UI组件
function initializeUI() {
    console.log('🖥️ 初始化UI组件...');

    try {
        // 检查必要的数据是否存在
        if (!window.cryptos) {
            console.warn('⚠️ window.cryptos数据尚未加载，延迟初始化UI');
            setTimeout(initializeUI, 500);
            return;
        }

        console.log(`📊 检测到 ${window.cryptos.length} 种加密货币数据`);

        // 生成加密货币网格
        updateCryptoGrid();

        // 生成持仓分布
        updatePositionChart();

        // 更新余额显示
        updateBalanceDisplay();

        // 更新交易记录
        updateTradeTable();

        // 设置响应式布局
        updateResponsiveLayout();

        console.log('✅ UI组件初始化完成');
    } catch (error) {
        console.error('❌ UI组件初始化失败:', error);
    }
}

// 生成百分比选项 (常用范围)
function generatePercentageOptions(type) {
    // 常用的百分比选项，减少选择数量
    const commonValues = [
        '1.0', '1.5', '2.0', '2.5', '3.0', '3.5', '4.0', '4.5', '5.0',
        '6.0', '7.0', '8.0', '9.0', '10.0', '12.0', '15.0', '20.0'
    ];

    let options = '';
    commonValues.forEach(value => {
        const selected = (type === 'profit' && value === '5.0') || (type === 'loss' && value === '3.0') ? 'selected' : '';
        options += `<option value="${value}" ${selected}>${value}%</option>`;
    });

    return options;
}

// 更新加密货币网格 - 保持原有设计，只替换数据源
function updateCryptoGrid() {
    console.log('🔄 开始更新加密货币网格...');

    const grid = document.getElementById('cryptoGrid');
    if (!grid) {
        console.error('❌ 找不到cryptoGrid元素');
        return;
    }

    if (!window.cryptos) {
        console.error('❌ window.cryptos数据不存在');
        return;
    }

    console.log(`📊 准备渲染 ${window.cryptos.length} 种加密货币`);

    // 检查格式化函数是否存在
    const formatPrice = window.formatPrice || ((price) => price.toFixed(2));
    const formatChange = window.formatChange || ((change) => `${change >= 0 ? '+' : ''}${change.toFixed(2)}%`);

    try {
        grid.innerHTML = window.cryptos.map(crypto => `
            <div class="crypto-item">
                <div class="crypto-symbol">${crypto.symbol}</div>
                <div class="crypto-name">${crypto.name}</div>
                <div class="crypto-controls">
                    <button class="leverage-btn-small">1x</button>
                    <button class="all-button-small">ALL</button>
                </div>
                <div class="crypto-price">$${formatPrice(crypto.price)}</div>
                <div class="crypto-change ${crypto.change >= 0 ? 'price-up' : 'price-down'}">
                    ${formatChange(crypto.change)}
                </div>
                <div class="profit-loss-controls">
                    <select class="profit-select" title="止盈百分比">
                        ${generatePercentageOptions('profit')}
                    </select>
                    <select class="loss-select" title="止损百分比">
                        ${generatePercentageOptions('loss')}
                    </select>
                </div>
                <div class="crypto-amount-input">
                    <input type="number" class="amount-input" placeholder="金额" min="0" step="0.01">
                </div>
                <div class="crypto-actions">
                    <button class="buy-btn" onclick="tradeWithAmount('${crypto.symbol}', 'buy')">买入</button>
                    <button class="sell-btn" onclick="tradeWithAmount('${crypto.symbol}', 'sell')">卖出</button>
                </div>
            </div>
        `).join('');

        console.log('✅ 加密货币网格更新成功');
    } catch (error) {
        console.error('❌ 更新加密货币网格失败:', error);
    }
}

// 更新持仓分布图表
function updatePositionChart() {
    const chart = document.getElementById('positionChart');
    if (!chart || !window.positions) return;
    
    chart.innerHTML = window.positions.map(position => `
        <div class="position-item">
            <div class="position-info">
                <div class="position-indicator" style="background: ${getPositionColor(position.symbol)}"></div>
                <div class="position-symbol">${position.symbol}</div>
            </div>
            <div class="position-details">
                <div class="position-percentage">${position.percentage}%</div>
                <div class="position-value">${position.value}</div>
            </div>
        </div>
    `).join('');
}

// 获取持仓颜色
function getPositionColor(symbol) {
    const colors = {
        'BTC': '#f7931a',
        'ETH': '#627eea',
        'BNB': '#f3ba2f',
        'SOL': '#9945ff',
        'XRP': '#23292f',
        'ADA': '#0033ad'
    };
    return colors[symbol] || '#10b981';
}

// 更新余额显示 - 支持现货和杠杆两个独立账户
function updateBalanceDisplay() {
    console.log('🔄 更新余额显示...');
    console.log('API状态:', window.apiStatus);
    console.log('现货账户余额:', window.spotAccountBalance);
    console.log('杠杆账户余额:', window.marginAccountBalance);

    // 检查DOM元素是否存在
    const spotTotalBalance = document.getElementById('spotTotalBalance');
    const marginTotalBalance = document.getElementById('marginTotalBalance');
    console.log('DOM元素检查:');
    console.log('- spotTotalBalance:', spotTotalBalance);
    console.log('- marginTotalBalance:', marginTotalBalance);

    // 检查是否已登录币安API
    if (window.apiStatus && window.apiStatus.connected) {
        console.log('✅ API已连接，更新真实余额');

        // 更新现货账户余额
        console.log('🔄 更新现货账户显示...');
        updateSpotAccountDisplay();

        // 更新杠杆账户余额
        console.log('🔄 更新杠杆账户显示...');
        updateMarginAccountDisplay();

    } else {
        console.log('❌ API未连接，显示提示信息');
        // 未登录，显示提示信息
        updateSpotAccountDisplay(false);
        updateMarginAccountDisplay(false);
    }

    console.log('✅ 余额显示更新完成');
}

// 更新现货账户显示
function updateSpotAccountDisplay(isConnected = true) {
    console.log('📊 更新现货账户显示, isConnected:', isConnected);
    console.log('📊 现货账户数据:', window.spotAccountBalance);

    const spotTotalBalance = document.getElementById('spotTotalBalance');
    const spotAvailableBalance = document.getElementById('spotAvailableBalance');
    const spotFrozenBalance = document.getElementById('spotFrozenBalance');
    const spotDailyPnL = document.getElementById('spotDailyPnL');
    const spotBalanceChange = document.getElementById('spotBalanceChange');

    console.log('📊 DOM元素检查:');
    console.log('- spotTotalBalance:', spotTotalBalance);
    console.log('- spotAvailableBalance:', spotAvailableBalance);

    if (isConnected && window.spotAccountBalance) {
        console.log('✅ 显示真实现货余额');
        // 显示真实现货余额
        if (spotTotalBalance) {
            const balance = `$${window.spotAccountBalance.totalBalance.toFixed(2)}`;
            console.log('💰 设置现货总余额:', balance);
            spotTotalBalance.textContent = balance;
            spotTotalBalance.style.color = '#10b981';
        } else {
            console.error('❌ spotTotalBalance元素未找到');
        }

        if (spotAvailableBalance) {
            const available = `$${window.spotAccountBalance.availableBalance.toFixed(2)}`;
            console.log('💰 设置现货可用余额:', available);
            spotAvailableBalance.textContent = available;
        }

        if (spotFrozenBalance) {
            const frozen = `$${window.spotAccountBalance.frozenBalance.toFixed(2)}`;
            console.log('💰 设置现货冻结余额:', frozen);
            spotFrozenBalance.textContent = frozen;
        }

        if (spotDailyPnL) {
            const pnl = window.spotAccountBalance.dailyPnL || 0;
            const sign = pnl >= 0 ? '+' : '';
            spotDailyPnL.textContent = `${sign}$${pnl.toFixed(2)}`;
            spotDailyPnL.className = `balance-item-value ${pnl >= 0 ? 'profit' : 'loss'}`;
        }

        if (spotBalanceChange) {
            const change = window.spotAccountBalance.dailyChangePercent || 0;
            const sign = change >= 0 ? '+' : '';
            spotBalanceChange.textContent = `24h变化 ${sign}${change.toFixed(2)}%`;
        }

        console.log('💰 现货账户余额已更新');
    } else {
        console.log('❌ 显示未登录状态');
        // 未登录状态
        if (spotTotalBalance) {
            spotTotalBalance.textContent = '请先登录';
            spotTotalBalance.style.color = '#9ca3af';
        }

        [spotAvailableBalance, spotFrozenBalance, spotDailyPnL].forEach(element => {
            if (element) element.textContent = '--';
        });

        if (spotBalanceChange) {
            spotBalanceChange.textContent = '24h变化 --';
        }
    }
}

// 更新杠杆账户显示
function updateMarginAccountDisplay(isConnected = true) {
    console.log('⚡ 更新杠杆账户显示, isConnected:', isConnected);
    console.log('⚡ 杠杆账户数据:', window.marginAccountBalance);

    const marginTotalBalance = document.getElementById('marginTotalBalance');
    const marginNetAsset = document.getElementById('marginNetAsset');
    const marginBorrowed = document.getElementById('marginBorrowed');
    const marginRiskLevel = document.getElementById('marginRiskLevel');
    const marginDailyPnL = document.getElementById('marginDailyPnL');
    const marginBalanceChange = document.getElementById('marginBalanceChange');

    console.log('⚡ DOM元素检查:');
    console.log('- marginTotalBalance:', marginTotalBalance);
    console.log('- marginNetAsset:', marginNetAsset);

    if (isConnected && window.marginAccountBalance) {
        console.log('✅ 显示真实杠杆余额');
        // 显示真实杠杆余额
        if (marginTotalBalance) {
            const balance = `$${window.marginAccountBalance.totalAsset.toFixed(2)}`;
            console.log('⚡ 设置杠杆总资产:', balance);
            marginTotalBalance.textContent = balance;
            marginTotalBalance.style.color = '#f59e0b';
        } else {
            console.error('❌ marginTotalBalance元素未找到');
        }

        if (marginNetAsset) {
            const netAsset = `$${window.marginAccountBalance.netAsset.toFixed(2)}`;
            console.log('⚡ 设置杠杆净资产:', netAsset);
            marginNetAsset.textContent = netAsset;
        }

        if (marginBorrowed) {
            const borrowed = `$${window.marginAccountBalance.totalLiability.toFixed(2)}`;
            console.log('⚡ 设置杠杆借贷:', borrowed);
            marginBorrowed.textContent = borrowed;
        }

        if (marginRiskLevel) {
            const riskLevel = calculateRiskLevel(window.marginAccountBalance.marginLevel);
            marginRiskLevel.textContent = riskLevel.text;
            marginRiskLevel.className = `balance-item-value risk-level ${riskLevel.class}`;
        }

        if (marginDailyPnL) {
            const pnl = window.marginAccountBalance.dailyPnL || 0;
            const sign = pnl >= 0 ? '+' : '';
            marginDailyPnL.textContent = `${sign}$${pnl.toFixed(2)}`;
            marginDailyPnL.className = `balance-item-value ${pnl >= 0 ? 'profit' : 'loss'}`;
        }

        if (marginBalanceChange) {
            const change = window.marginAccountBalance.dailyChangePercent || 0;
            const sign = change >= 0 ? '+' : '';
            marginBalanceChange.textContent = `24h变化 ${sign}${change.toFixed(2)}%`;
        }

        console.log('⚡ 杠杆账户余额已更新');
    } else {
        console.log('❌ 显示未登录状态');
        // 未登录状态
        if (marginTotalBalance) {
            marginTotalBalance.textContent = '请先登录';
            marginTotalBalance.style.color = '#9ca3af';
        }

        [marginNetAsset, marginBorrowed, marginDailyPnL].forEach(element => {
            if (element) element.textContent = '--';
        });

        if (marginRiskLevel) {
            marginRiskLevel.textContent = '--';
            marginRiskLevel.className = 'balance-item-value risk-level';
        }

        if (marginBalanceChange) {
            marginBalanceChange.textContent = '24h变化 --';
        }
    }
}

// 计算风险等级
function calculateRiskLevel(marginLevel) {
    if (!marginLevel || marginLevel >= 3) {
        return { text: '安全', class: 'safe' };
    } else if (marginLevel >= 1.5) {
        return { text: '警告', class: 'warning' };
    } else {
        return { text: '危险', class: 'danger' };
    }
}

// 更新交易记录表格 - 显示实盘交易记录
function updateTradeTable() {
    const table = document.getElementById('tradeTable');
    if (!table || !window.tradeHistory) return;

    if (window.tradeHistory.length === 0) {
        table.innerHTML = `
            <tr>
                <td colspan="7" class="table-cell" style="text-align: center; color: #9ca3af; padding: 20px;">
                    📊 暂无交易记录<br>
                    <small style="color: #6b7280;">开始交易后，记录将显示在这里</small>
                </td>
            </tr>
        `;
        return;
    }

    table.innerHTML = window.tradeHistory.map(trade => {
        // 格式化交易对显示
        const displaySymbol = trade.symbol.includes('USDT')
            ? trade.symbol.replace('USDT', '/USDT')
            : trade.symbol;

        // 格式化交易类型（包含现货/杠杆标注）
        const baseType = trade.type === 'buy' ? '💰 买入' : '💸 卖出';
        const accountTypeDisplay = trade.accountType === 'margin' ? '杠杆' : '现货';
        const accountTypeColor = trade.accountType === 'margin' ? '#ff6b6b' : '#4ecdc4';
        const typeDisplay = `${baseType}<br><small style="color: ${accountTypeColor}; font-size: 9px;">${accountTypeDisplay}</small>`;
        const typeClass = trade.type === 'buy' ? 'price-up' : 'price-down';

        // 格式化数量显示
        const amountDisplay = typeof trade.amount === 'number'
            ? trade.amount.toFixed(6)
            : trade.amount;

        // 格式化价格显示
        const priceDisplay = typeof trade.price === 'number'
            ? `$${window.formatPrice(trade.price)}`
            : trade.price;

        // 格式化盈亏显示
        const profitDisplay = typeof trade.profit === 'number'
            ? `${trade.profit >= 0 ? '+' : ''}$${trade.profit.toFixed(2)}`
            : trade.profit;
        const profitClass = (typeof trade.profit === 'number' && trade.profit >= 0) ? 'price-up' : 'price-down';

        // 格式化状态显示
        const statusDisplay = trade.status === '已完成' ? '✅ 已完成' :
                             trade.status === '部分成交' ? '⏳ 部分成交' :
                             trade.status;

        return `
            <tr title="交易ID: ${trade.id || 'N/A'}">
                <td class="table-cell">${trade.time}</td>
                <td class="table-cell" style="font-weight: 600;">${displaySymbol}</td>
                <td class="table-cell">
                    <span class="${typeClass}" style="font-weight: 600;">
                        ${typeDisplay}
                    </span>
                </td>
                <td class="table-cell">${amountDisplay}</td>
                <td class="table-cell" style="font-family: monospace;">${priceDisplay}</td>
                <td class="table-cell">
                    <span class="${profitClass}" style="font-weight: 600;">
                        ${profitDisplay}
                    </span>
                </td>
                <td class="table-cell">
                    <span class="price-up" style="font-size: 11px;">
                        ${statusDisplay}
                    </span>
                </td>
            </tr>
        `;
    }).join('');

    console.log(`📊 交易记录表格已更新 (${window.tradeHistory.length}条记录)`);
}

// 更新响应式布局
function updateResponsiveLayout() {
    const width = window.innerWidth;
    const grid = document.getElementById('cryptoGrid');
    
    if (!grid) return;
    
    // 根据屏幕宽度调整网格列数
    if (width <= 360) {
        grid.style.gridTemplateColumns = 'repeat(2, 1fr)';
    } else if (width <= 480) {
        grid.style.gridTemplateColumns = 'repeat(3, 1fr)';
    } else if (width <= 768) {
        grid.style.gridTemplateColumns = 'repeat(4, 1fr)';
    } else if (width <= 1024) {
        grid.style.gridTemplateColumns = 'repeat(5, 1fr)';
    } else {
        grid.style.gridTemplateColumns = 'repeat(7, 1fr)';
    }
    
    console.log(`📱 响应式布局更新: ${width}px`);
}

// 显示加载状态
function showLoading(element) {
    if (element) {
        element.innerHTML = `
            <div style="display: flex; justify-content: center; align-items: center; padding: 20px;">
                <div style="color: #9ca3af;">加载中...</div>
            </div>
        `;
    }
}

// 隐藏加载状态
function hideLoading() {
    // 移除所有加载状态
    document.querySelectorAll('.loading').forEach(el => {
        el.classList.remove('loading');
    });
}

// 添加动画效果
function addAnimation(element, animationType = 'fadeIn') {
    if (!element) return;
    
    element.style.opacity = '0';
    element.style.transform = 'translateY(10px)';
    element.style.transition = 'all 0.3s ease';
    
    setTimeout(() => {
        element.style.opacity = '1';
        element.style.transform = 'translateY(0)';
    }, 50);
}

// 高亮价格变化
function highlightPriceChange(element, isIncrease) {
    if (!element) return;
    
    const color = isIncrease ? '#00ff88' : '#ff4757';
    const originalColor = element.style.color;
    
    element.style.color = color;
    element.style.transform = 'scale(1.05)';
    element.style.transition = 'all 0.2s ease';
    
    setTimeout(() => {
        element.style.color = originalColor;
        element.style.transform = 'scale(1)';
    }, 500);
}

// 更新实时指示器
function updateRealTimeIndicator() {
    const indicator = document.querySelector('.update-indicator');
    if (indicator) {
        const now = new Date();
        indicator.textContent = `最后更新: ${now.toLocaleTimeString('zh-CN')}`;
        
        // 添加闪烁效果
        indicator.style.opacity = '0.5';
        setTimeout(() => {
            indicator.style.opacity = '1';
        }, 200);
    }
}

// 创建通知消息
function createNotification(message, type = 'info', duration = 3000) {
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.textContent = message;
    
    // 设置样式
    Object.assign(notification.style, {
        position: 'fixed',
        top: '20px',
        right: '20px',
        padding: '12px 20px',
        borderRadius: '6px',
        color: 'white',
        fontSize: '14px',
        fontWeight: 'bold',
        zIndex: '9999',
        opacity: '0',
        transform: 'translateX(100%)',
        transition: 'all 0.3s ease'
    });
    
    // 设置背景色
    const colors = {
        success: '#10b981',
        error: '#ef4444',
        warning: '#f59e0b',
        info: '#3b82f6'
    };
    notification.style.background = colors[type] || colors.info;
    
    document.body.appendChild(notification);
    
    // 显示动画
    setTimeout(() => {
        notification.style.opacity = '1';
        notification.style.transform = 'translateX(0)';
    }, 50);
    
    // 自动隐藏
    setTimeout(() => {
        notification.style.opacity = '0';
        notification.style.transform = 'translateX(100%)';
        setTimeout(() => {
            document.body.removeChild(notification);
        }, 300);
    }, duration);
}

// 格式化数字显示
function formatNumberDisplay(number, decimals = 2) {
    if (number >= 1000000) {
        return (number / 1000000).toFixed(1) + 'M';
    } else if (number >= 1000) {
        return (number / 1000).toFixed(1) + 'K';
    } else {
        return number.toFixed(decimals);
    }
}

// 创建进度条
function createProgressBar(container, percentage) {
    if (!container) return;
    
    const progressBar = document.createElement('div');
    progressBar.className = 'progress-bar';
    
    Object.assign(progressBar.style, {
        width: '100%',
        height: '4px',
        background: 'rgba(255, 255, 255, 0.1)',
        borderRadius: '2px',
        overflow: 'hidden'
    });
    
    const progressFill = document.createElement('div');
    progressFill.className = 'progress-fill';
    
    Object.assign(progressFill.style, {
        width: `${percentage}%`,
        height: '100%',
        background: 'linear-gradient(90deg, #00ff88, #10b981)',
        borderRadius: '2px',
        transition: 'width 0.3s ease'
    });
    
    progressBar.appendChild(progressFill);
    container.appendChild(progressBar);
    
    return progressBar;
}

// 导出函数到全局作用域
window.initializeUI = initializeUI;
window.updateCryptoGrid = updateCryptoGrid;
window.updatePositionChart = updatePositionChart;
window.updateBalanceDisplay = updateBalanceDisplay;
window.updateTradeTable = updateTradeTable;
window.updateResponsiveLayout = updateResponsiveLayout;
window.showLoading = showLoading;
window.hideLoading = hideLoading;
window.addAnimation = addAnimation;
window.highlightPriceChange = highlightPriceChange;
window.updateRealTimeIndicator = updateRealTimeIndicator;
window.createNotification = createNotification;
window.formatNumberDisplay = formatNumberDisplay;
window.createProgressBar = createProgressBar;

console.log('🖥️ UI更新模块已加载');
