// 断金交易系统 - BTC K线图表模块
// 功能：实时BTC K线图表显示，支持多周期切换
// 数据源：币安API实盘数据

// K线图表配置
const KLINE_CONFIG = {
    symbol: 'BTCUSDT',           // 交易对
    defaultInterval: '1m',       // 默认周期
    limit: 100,                  // K线数量
    updateInterval: 5000,        // 更新间隔(毫秒)
    colors: {
        up: '#00ff00',           // 上涨颜色
        down: '#ff0040',         // 下跌颜色
        border: '#00ff00',       // 边框颜色
        text: '#00ff00',         // 文字颜色
        grid: '#333333'          // 网格颜色
    }
};

// 全局变量
let currentInterval = KLINE_CONFIG.defaultInterval;
let klineData = [];
let chartCanvas = null;
let chartContext = null;
let updateTimer = null;
let isLoading = false;

// 缩放控制变量
let zoomLevel = 1.0;        // 缩放级别
let candleWidth = 8;        // K线宽度
let candleSpacing = 10;     // K线间距
let visibleCandles = 50;    // 可见K线数量
let scrollOffset = 0;       // 滚动偏移

// 支撑位和压力位
let supportLevels = [];     // 支撑位数组
let resistanceLevels = [];  // 压力位数组
let showSupportResistance = true; // 是否显示支撑压力位

// 拖拽控制变量
let isDragging = false;     // 是否正在拖拽
let dragStartX = 0;         // 拖拽开始X坐标
let dragStartOffset = 0;    // 拖拽开始时的偏移量

// 窗口拖拽和调整大小变量
let isWindowDragging = false;    // 是否正在拖拽窗口
let isWindowResizing = false;    // 是否正在调整窗口大小
let windowDragStartX = 0;        // 窗口拖拽开始X坐标
let windowDragStartY = 0;        // 窗口拖拽开始Y坐标
let windowStartLeft = 0;         // 窗口初始左边距
let windowStartTop = 0;          // 窗口初始上边距
let resizeStartX = 0;            // 调整大小开始X坐标
let resizeStartY = 0;            // 调整大小开始Y坐标
let resizeStartWidth = 0;        // 调整大小开始宽度
let resizeStartHeight = 0;       // 调整大小开始高度

// 响应式布局变量
let isUserPositioned = false;    // 用户是否手动调整过位置
let lastWindowWidth = window.innerWidth;   // 上次窗口宽度
let lastWindowHeight = window.innerHeight; // 上次窗口高度

// 初始化K线图表
function initializeKlineChart() {
    console.log('📈 初始化BTC K线图表...');

    try {
        // 获取图表容器
        const chartContainer = document.getElementById('kline-chart');
        if (!chartContainer) {
            console.error('❌ 找不到K线图表容器 #kline-chart');
            return false;
        }

        console.log('✅ 找到K线图表容器:', chartContainer);

        // 创建Canvas元素
        chartCanvas = document.createElement('canvas');
        // 设置Canvas的实际像素尺寸
        const containerRect = chartContainer.getBoundingClientRect();
        chartCanvas.width = containerRect.width || 470;
        chartCanvas.height = containerRect.height || 280;
        chartCanvas.style.width = '100%';
        chartCanvas.style.height = '100%';
        
        chartContext = chartCanvas.getContext('2d');
        chartContainer.appendChild(chartCanvas);

        // 绑定鼠标滚轮事件
        chartCanvas.addEventListener('wheel', handleMouseWheel, { passive: false });

        // 绑定拖拽事件
        chartCanvas.addEventListener('mousedown', handleMouseDown);
        document.addEventListener('mousemove', handleMouseMove); // 全局监听
        document.addEventListener('mouseup', handleMouseUp);     // 全局监听
        chartCanvas.addEventListener('mouseleave', handleMouseUp);
        chartCanvas.addEventListener('mouseenter', () => {
            if (!isDragging) chartCanvas.style.cursor = 'grab';
        });

        // 设置canvas样式
        chartCanvas.style.cursor = 'grab';
        chartCanvas.style.userSelect = 'none';

        // 绑定键盘事件
        chartCanvas.setAttribute('tabindex', '0'); // 使canvas可以获得焦点
        chartCanvas.addEventListener('keydown', handleKeyDown);

        // 绑定周期选择器事件
        bindTimeframeEvents();

        // 绑定缩放控制事件
        bindZoomEvents();

        // 绑定窗口拖拽和调整大小事件
        bindWindowDragEvents();

        // 监听窗口大小变化和缩放
        window.addEventListener('resize', function() {
            console.log(`🔄 窗口大小变化检测: ${window.innerWidth}x${window.innerHeight}`);
            setTimeout(() => {
                handleWindowResize();
            }, 100);
        });

        // 监听浏览器缩放 - 使用多种方法
        let lastZoom = window.devicePixelRatio;
        let lastInnerWidth = window.innerWidth;
        let lastInnerHeight = window.innerHeight;

        // 方法1: 监听devicePixelRatio变化
        setInterval(() => {
            if (window.devicePixelRatio !== lastZoom) {
                console.log(`🔍 浏览器缩放变化: ${lastZoom} -> ${window.devicePixelRatio}`);
                lastZoom = window.devicePixelRatio;
                setTimeout(() => {
                    handleWindowResize();
                }, 100);
            }
        }, 300);

        // 方法2: 监听视窗尺寸变化（包括缩放引起的变化）
        setInterval(() => {
            if (window.innerWidth !== lastInnerWidth || window.innerHeight !== lastInnerHeight) {
                console.log(`📐 视窗尺寸变化: ${lastInnerWidth}x${lastInnerHeight} -> ${window.innerWidth}x${window.innerHeight}`);
                lastInnerWidth = window.innerWidth;
                lastInnerHeight = window.innerHeight;
                setTimeout(() => {
                    handleWindowResize();
                }, 50);
            }
        }, 200);

        // 方法3: 监听orientationchange事件（移动设备）
        window.addEventListener('orientationchange', function() {
            console.log('📱 设备方向变化');
            setTimeout(() => {
                handleWindowResize();
            }, 300);
        });

        // 方法4: 使用MutationObserver监听DOM变化
        const klineSection = document.querySelector('.kline-chart-section');
        if (klineSection) {
            const observer = new MutationObserver((mutations) => {
                mutations.forEach((mutation) => {
                    if (mutation.type === 'attributes' &&
                        (mutation.attributeName === 'style' || mutation.attributeName === 'class')) {
                        console.log('🔍 K线图DOM属性变化');
                        setTimeout(() => {
                            resizeCanvas();
                            drawKlineChart();
                        }, 50);
                    }
                });
            });

            observer.observe(klineSection, {
                attributes: true,
                attributeFilter: ['style', 'class']
            });
        }

        // 方法5: 使用ResizeObserver监听元素大小变化（现代浏览器）
        if (window.ResizeObserver && klineSection) {
            const resizeObserver = new ResizeObserver((entries) => {
                for (let entry of entries) {
                    console.log('📏 K线图容器大小变化:', entry.contentRect);
                    setTimeout(() => {
                        resizeCanvas();
                        drawKlineChart();
                    }, 50);
                }
            });

            resizeObserver.observe(klineSection);
        }

        // 加载初始数据
        loadKlineData(currentInterval);

        // 启动定时更新
        startAutoUpdate();

        // 初始化时检查并调整位置
        setTimeout(() => {
            const klineSection = document.querySelector('.kline-chart-section');
            if (klineSection) {
                // 强制进行一次自适应调整
                adjustKlinePosition(klineSection, window.innerWidth, window.innerHeight);
                resizeCanvas();
                drawKlineChart();
                console.log('🔄 K线图初始化自适应调整完成');
            }
        }, 500);

        console.log('✅ K线图表初始化完成');
        return true;

    } catch (error) {
        console.error('❌ K线图表初始化失败:', error);
        return false;
    }
}

// 绑定周期选择器事件
function bindTimeframeEvents() {
    const timeframeBtns = document.querySelectorAll('.timeframe-btn');

    timeframeBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // 移除所有active类
            timeframeBtns.forEach(b => b.classList.remove('active'));

            // 添加active类到当前按钮
            this.classList.add('active');

            // 获取新的时间周期
            const newInterval = this.getAttribute('data-interval');

            if (newInterval !== currentInterval) {
                currentInterval = newInterval;
                console.log(`📊 切换到${newInterval}周期`);

                // 重新加载数据
                loadKlineData(currentInterval);
            }
        });
    });
}

// 绑定缩放控制事件
function bindZoomEvents() {
    const zoomResetBtn = document.getElementById('zoom-reset');
    const refreshBtn = document.getElementById('refresh-kline');

    if (zoomResetBtn) {
        zoomResetBtn.addEventListener('click', function() {
            // 重置缩放和滚动
            zoomLevel = 1.0;
            scrollOffset = 0;
            updateZoomSettings();
            drawKlineChart();

            // 重置位置为自动模式
            resetKlinePosition();

            console.log('⚡ 重置视图和位置');
        });
    }

    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            // 强制刷新自适应
            forceKlineRefresh();
            console.log('🔄 手动刷新K线图自适应');
        });
    }
}

// 重置K线图位置为自动模式
function resetKlinePosition() {
    const klineSection = document.querySelector('.kline-chart-section');
    if (!klineSection) return;

    // 重置为自动定位模式
    isUserPositioned = false;

    // 强制进行自适应调整
    adjustKlinePosition(klineSection, window.innerWidth, window.innerHeight);
    resizeCanvas();
    drawKlineChart();

    console.log('🔄 K线图位置已重置为自动模式');
}

// 强制刷新K线图自适应
function forceKlineRefresh() {
    const klineSection = document.querySelector('.kline-chart-section');
    if (!klineSection) return;

    console.log('🔄 强制刷新K线图自适应...');

    // 重新计算并应用自适应
    adjustKlinePosition(klineSection, window.innerWidth, window.innerHeight);
    resizeCanvas();
    drawKlineChart();

    console.log('✅ K线图自适应刷新完成');
}

// 导出到全局作用域
window.forceKlineRefresh = forceKlineRefresh;

// 绑定窗口拖拽和调整大小事件
function bindWindowDragEvents() {
    const klineSection = document.querySelector('.kline-chart-section');
    const chartHeader = document.querySelector('.chart-header');

    if (!klineSection || !chartHeader) {
        console.error('❌ 找不到K线图表容器或头部');
        return;
    }

    // 头部拖拽事件（移动窗口）
    chartHeader.addEventListener('mousedown', function(e) {
        // 避免与按钮冲突
        if (e.target.tagName === 'BUTTON') return;

        isWindowDragging = true;
        windowDragStartX = e.clientX;
        windowDragStartY = e.clientY;

        const rect = klineSection.getBoundingClientRect();
        windowStartLeft = rect.left;
        windowStartTop = rect.top;

        klineSection.classList.add('dragging');
        chartHeader.style.cursor = 'grabbing';

        e.preventDefault();
        console.log('🖱️ 开始拖拽K线窗口');
    });

    // 调整大小事件（右下角）
    klineSection.addEventListener('mousedown', function(e) {
        const rect = klineSection.getBoundingClientRect();
        const isResizeArea = (
            e.clientX > rect.right - 20 &&
            e.clientY > rect.bottom - 20
        );

        if (isResizeArea) {
            isWindowResizing = true;
            resizeStartX = e.clientX;
            resizeStartY = e.clientY;
            resizeStartWidth = rect.width;
            resizeStartHeight = rect.height;

            klineSection.classList.add('resizing');

            e.preventDefault();
            e.stopPropagation();
            console.log('📏 开始调整K线窗口大小');
        }
    });

    // 全局鼠标移动事件
    document.addEventListener('mousemove', function(e) {
        if (isWindowDragging) {
            const deltaX = e.clientX - windowDragStartX;
            const deltaY = e.clientY - windowDragStartY;

            const newLeft = windowStartLeft + deltaX;
            const newTop = windowStartTop + deltaY;

            // 限制在窗口范围内
            const maxLeft = window.innerWidth - klineSection.offsetWidth;
            const maxTop = window.innerHeight - klineSection.offsetHeight;

            const constrainedLeft = Math.max(0, Math.min(newLeft, maxLeft));
            const constrainedTop = Math.max(0, Math.min(newTop, maxTop));

            klineSection.style.left = constrainedLeft + 'px';
            klineSection.style.top = constrainedTop + 'px';
            klineSection.style.right = 'auto';
            klineSection.style.bottom = 'auto';

        } else if (isWindowResizing) {
            const deltaX = e.clientX - resizeStartX;
            const deltaY = e.clientY - resizeStartY;

            const newWidth = Math.max(280, Math.min(800, resizeStartWidth + deltaX));
            const newHeight = Math.max(120, Math.min(600, resizeStartHeight + deltaY));

            klineSection.style.width = newWidth + 'px';
            klineSection.style.height = newHeight + 'px';

            // 重新调整canvas大小
            if (chartCanvas) {
                setTimeout(() => {
                    resizeCanvas();
                    drawKlineChart();
                }, 10);
            }
        }
    });

    // 全局鼠标释放事件
    document.addEventListener('mouseup', function() {
        if (isWindowDragging) {
            isWindowDragging = false;
            isUserPositioned = true; // 标记为用户自定义位置
            klineSection.classList.remove('dragging');
            chartHeader.style.cursor = 'move';
            console.log('✅ 完成K线窗口拖拽 - 已标记为用户自定义位置');
        }

        if (isWindowResizing) {
            isWindowResizing = false;
            klineSection.classList.remove('resizing');
            console.log('✅ 完成K线窗口大小调整');
        }
    });
}

// 更新缩放设置
function updateZoomSettings() {
    candleWidth = Math.max(2, Math.floor(8 * zoomLevel));
    candleSpacing = Math.max(3, Math.floor(10 * zoomLevel));

    if (chartCanvas) {
        const availableWidth = chartCanvas.width - 40;
        visibleCandles = Math.floor(availableWidth / candleSpacing);
        visibleCandles = Math.max(10, Math.min(visibleCandles, klineData.length));
    }
}

// 处理窗口大小变化
function handleWindowResize() {
    const currentWidth = window.innerWidth;
    const currentHeight = window.innerHeight;

    // 计算窗口大小变化比例
    const widthRatio = currentWidth / lastWindowWidth;
    const heightRatio = currentHeight / lastWindowHeight;

    const klineSection = document.querySelector('.kline-chart-section');
    if (!klineSection) return;

    // 如果用户没有手动调整过位置，使用智能定位
    if (!isUserPositioned) {
        adjustKlinePosition(klineSection, currentWidth, currentHeight);
    } else {
        // 用户手动调整过位置，按比例调整
        adjustUserPosition(klineSection, widthRatio, heightRatio);
    }

    // 调整Canvas大小
    resizeCanvas();
    drawKlineChart();

    // 更新记录的窗口大小
    lastWindowWidth = currentWidth;
    lastWindowHeight = currentHeight;

    console.log(`🔄 窗口大小变化: ${currentWidth}x${currentHeight}`);
}

// 智能调整K线图位置（未手动调整时）
function adjustKlinePosition(klineSection, windowWidth, windowHeight) {
    // 获取当前尺寸
    const rect = klineSection.getBoundingClientRect();
    const sectionWidth = rect.width || 450;
    const sectionHeight = rect.height || 160;

    // 考虑浏览器缩放比例
    const zoomRatio = window.devicePixelRatio || 1;
    const effectiveWidth = windowWidth * zoomRatio;
    const effectiveHeight = windowHeight * zoomRatio;

    // 响应式位置和尺寸计算
    let rightMargin, bottomMargin, newWidth, newHeight;

    if (windowWidth <= 480) {
        // 超小屏幕
        rightMargin = Math.max(10, windowWidth * 0.025);
        bottomMargin = Math.max(10, windowHeight * 0.05);
        newWidth = Math.min(windowWidth * 0.95, 400);
        newHeight = Math.min(windowHeight * 0.25, 200);

    } else if (windowWidth <= 768) {
        // 小屏幕
        rightMargin = Math.max(20, windowWidth * 0.05);
        bottomMargin = Math.max(20, windowHeight * 0.1);
        newWidth = Math.min(windowWidth * 0.9, 450);
        newHeight = Math.min(windowHeight * 0.3, 250);

    } else if (windowWidth <= 1200) {
        // 中等屏幕
        rightMargin = Math.max(50, windowWidth * 0.08);
        bottomMargin = Math.max(50, windowHeight * 0.15);
        newWidth = Math.min(windowWidth * 0.4, 500);
        newHeight = Math.min(windowHeight * 0.35, 300);

    } else {
        // 大屏幕
        rightMargin = Math.max(280, windowWidth * 0.2);
        bottomMargin = Math.max(220, windowHeight * 0.2);
        newWidth = Math.min(windowWidth * 0.35, 600);
        newHeight = Math.min(windowHeight * 0.3, 350);
    }

    // 应用新的尺寸和位置
    klineSection.style.width = newWidth + 'px';
    klineSection.style.height = newHeight + 'px';
    klineSection.style.right = rightMargin + 'px';
    klineSection.style.bottom = bottomMargin + 'px';
    klineSection.style.left = 'auto';
    klineSection.style.top = 'auto';

    console.log(`📱 K线图自适应调整: ${windowWidth}x${windowHeight} (缩放:${zoomRatio.toFixed(2)}), 尺寸: ${newWidth}x${newHeight}, 位置: right=${rightMargin}px, bottom=${bottomMargin}px`);
}

// 按比例调整用户自定义位置
function adjustUserPosition(klineSection, widthRatio, heightRatio) {
    const currentStyle = window.getComputedStyle(klineSection);

    // 如果使用的是left/top定位
    if (klineSection.style.left && klineSection.style.left !== 'auto') {
        const currentLeft = parseInt(klineSection.style.left);
        const newLeft = Math.max(0, Math.min(currentLeft * widthRatio, window.innerWidth - klineSection.offsetWidth));
        klineSection.style.left = newLeft + 'px';
    }

    if (klineSection.style.top && klineSection.style.top !== 'auto') {
        const currentTop = parseInt(klineSection.style.top);
        const newTop = Math.max(0, Math.min(currentTop * heightRatio, window.innerHeight - klineSection.offsetHeight));
        klineSection.style.top = newTop + 'px';
    }

    // 如果使用的是right/bottom定位
    if (klineSection.style.right && klineSection.style.right !== 'auto') {
        const currentRight = parseInt(klineSection.style.right);
        const newRight = Math.max(0, Math.min(currentRight * widthRatio, window.innerWidth - klineSection.offsetWidth));
        klineSection.style.right = newRight + 'px';
    }

    if (klineSection.style.bottom && klineSection.style.bottom !== 'auto') {
        const currentBottom = parseInt(klineSection.style.bottom);
        const newBottom = Math.max(0, Math.min(currentBottom * heightRatio, window.innerHeight - klineSection.offsetHeight));
        klineSection.style.bottom = newBottom + 'px';
    }
}

// 重新调整Canvas大小
function resizeCanvas() {
    if (!chartCanvas) return;

    const chartContainer = document.getElementById('kline-chart');
    if (!chartContainer) return;

    const containerRect = chartContainer.getBoundingClientRect();
    const newWidth = containerRect.width || 400;
    const newHeight = containerRect.height || 200;

    // 只有当尺寸真正改变时才重新设置
    if (chartCanvas.width !== newWidth || chartCanvas.height !== newHeight) {
        chartCanvas.width = newWidth;
        chartCanvas.height = newHeight;

        console.log(`📏 Canvas大小调整为: ${newWidth}x${newHeight}`);

        // 重新计算可见K线数量
        updateZoomSettings();
    }
}

// 加载K线数据
async function loadKlineData(interval) {
    if (isLoading) return;
    
    isLoading = true;
    showLoading(true);
    
    try {
        console.log(`📡 获取${KLINE_CONFIG.symbol} ${interval}周期K线数据...`);
        
        const response = await fetch(`http://localhost:3001/api/binance/klines?symbol=${KLINE_CONFIG.symbol}&interval=${interval}&limit=${KLINE_CONFIG.limit}`);
        const data = await response.json();
        
        if (data.success && data.data) {
            // 处理K线数据
            klineData = data.data.map(kline => ({
                timestamp: kline[0],
                open: parseFloat(kline[1]),
                high: parseFloat(kline[2]),
                low: parseFloat(kline[3]),
                close: parseFloat(kline[4]),
                volume: parseFloat(kline[5])
            }));
            
            console.log(`✅ 获取到${klineData.length}条K线数据`);

            // 计算支撑位和压力位
            calculateSupportResistance(klineData);

            // 绘制图表
            drawKlineChart();

            // 更新统计信息
            updateChartStats();

            // 更新交易提示
            updateTradingHint();
            
        } else {
            console.error('❌ K线数据获取失败:', data.error || '未知错误');
            showError('K线数据获取失败');
        }
        
    } catch (error) {
        console.error('❌ K线数据请求异常:', error);
        showError('网络连接失败');
    } finally {
        isLoading = false;
        showLoading(false);
    }
}

// 绘制K线图表
function drawKlineChart() {
    if (!chartContext || !klineData.length) return;

    const canvas = chartCanvas;
    const ctx = chartContext;
    const width = canvas.width;
    const height = canvas.height;

    // 清空画布
    ctx.clearRect(0, 0, width, height);

    // 更新缩放设置
    updateZoomSettings();

    // 计算显示的K线数据范围
    const maxStartIndex = Math.max(0, klineData.length - visibleCandles);
    const startIndex = Math.max(0, maxStartIndex - scrollOffset);
    const endIndex = Math.min(klineData.length, startIndex + visibleCandles);
    const displayData = klineData.slice(startIndex, endIndex);

    if (displayData.length === 0) return;

    // 计算价格范围（基于显示的数据）
    const prices = displayData.flatMap(k => [k.high, k.low]);
    const maxPrice = Math.max(...prices);
    const minPrice = Math.min(...prices);
    const priceRange = maxPrice - minPrice;
    const padding = priceRange * 0.1; // 10%边距

    const chartTop = 20;
    const chartBottom = height - 40;
    const chartHeight = chartBottom - chartTop;

    // 绘制网格线
    drawGrid(ctx, width, height, chartTop, chartBottom, displayData.length);

    // 绘制K线
    displayData.forEach((kline, index) => {
        const x = 20 + index * candleSpacing + candleSpacing / 2;

        // 计算Y坐标
        const openY = chartBottom - ((kline.open - minPrice) / (priceRange + 2 * padding)) * chartHeight;
        const closeY = chartBottom - ((kline.close - minPrice) / (priceRange + 2 * padding)) * chartHeight;
        const highY = chartBottom - ((kline.high - minPrice) / (priceRange + 2 * padding)) * chartHeight;
        const lowY = chartBottom - ((kline.low - minPrice) / (priceRange + 2 * padding)) * chartHeight;

        // 确定颜色
        const isUp = kline.close >= kline.open;
        const color = isUp ? KLINE_CONFIG.colors.up : KLINE_CONFIG.colors.down;

        // 绘制影线
        ctx.strokeStyle = color;
        ctx.lineWidth = 1;
        ctx.beginPath();
        ctx.moveTo(x, highY);
        ctx.lineTo(x, lowY);
        ctx.stroke();

        // 绘制实体
        const bodyTop = Math.min(openY, closeY);
        const bodyHeight = Math.max(1, Math.abs(closeY - openY));

        if (bodyHeight < 2) {
            // 十字星
            ctx.strokeStyle = color;
            ctx.lineWidth = 1;
            ctx.beginPath();
            ctx.moveTo(x - candleWidth/2, openY);
            ctx.lineTo(x + candleWidth/2, openY);
            ctx.stroke();
        } else {
            // 实体K线
            if (isUp) {
                // 上涨：空心
                ctx.strokeStyle = color;
                ctx.lineWidth = 1;
                ctx.strokeRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
            } else {
                // 下跌：实心
                ctx.fillStyle = color;
                ctx.fillRect(x - candleWidth/2, bodyTop, candleWidth, bodyHeight);
            }
        }
    });

    // 绘制支撑位和压力位
    if (showSupportResistance) {
        drawSupportResistanceLevels(ctx, width, height, minPrice, maxPrice, padding);
    }

    // 绘制价格标签
    drawPriceLabels(ctx, width, height, minPrice, maxPrice, padding);

    // 显示缩放信息
    drawZoomInfo(ctx, width, height);
}

// 绘制网格线
function drawGrid(ctx, width, height, top, bottom, dataLength) {
    ctx.strokeStyle = KLINE_CONFIG.colors.grid;
    ctx.lineWidth = 0.5;

    // 水平网格线
    for (let i = 0; i <= 5; i++) {
        const y = top + (bottom - top) * i / 5;
        ctx.beginPath();
        ctx.moveTo(20, y);
        ctx.lineTo(width - 20, y);
        ctx.stroke();
    }

    // 垂直网格线
    const gridCount = Math.min(8, dataLength);
    for (let i = 0; i <= gridCount; i++) {
        const x = 20 + (width - 40) * i / gridCount;
        ctx.beginPath();
        ctx.moveTo(x, top);
        ctx.lineTo(x, bottom);
        ctx.stroke();
    }
}

// 绘制价格标签
function drawPriceLabels(ctx, width, height, minPrice, maxPrice, padding) {
    ctx.fillStyle = KLINE_CONFIG.colors.text;
    ctx.font = '10px Arial';
    ctx.textAlign = 'right';

    const priceRange = maxPrice - minPrice;
    for (let i = 0; i <= 5; i++) {
        const price = minPrice + priceRange * i / 5;
        const y = height - 40 - (height - 60) * i / 5;

        ctx.fillText(price.toFixed(2), width - 5, y + 3);
    }
}

// 绘制支撑位和压力位
function drawSupportResistanceLevels(ctx, width, height, minPrice, maxPrice, padding) {
    const priceRange = maxPrice - minPrice;
    const chartTop = 20;
    const chartBottom = height - 40;
    const chartHeight = chartBottom - chartTop;

    // 绘制支撑位（绿色虚线）
    supportLevels.forEach((level, index) => {
        if (level.price >= minPrice && level.price <= maxPrice) {
            const y = chartBottom - ((level.price - minPrice) / priceRange) * chartHeight;

            // 第二支撑位特殊标记（右侧交易重点关注）
            const isSecondLevel = level.rank === 2;
            const baseAlpha = Math.min(0.8, 0.4 + (level.strength * level.touches) * 0.1);
            const alpha = isSecondLevel ? Math.min(0.9, baseAlpha + 0.2) : baseAlpha;

            // 设置虚线样式
            if (isSecondLevel) {
                ctx.setLineDash([10, 3, 2, 3]); // 特殊虚线样式
            } else {
                ctx.setLineDash([8, 4]);
            }

            ctx.strokeStyle = `rgba(0, 255, 0, ${alpha})`;
            ctx.lineWidth = isSecondLevel ? Math.max(2, level.touches * 0.6) : Math.max(1, level.touches * 0.5);

            // 绘制水平线
            ctx.beginPath();
            ctx.moveTo(20, y);
            ctx.lineTo(width - 20, y);
            ctx.stroke();

            // 绘制标签背景
            const intervalNames = { '1m': '1分', '5m': '5分', '15m': '15分', '1h': '1时', '4h': '4时', '1d': '1日' };
            const intervalName = intervalNames[currentInterval] || currentInterval;
            const entryHint = isSecondLevel ? ' 🎯进场' : '';
            const labelText = `${intervalName}${level.label} ${level.price.toFixed(2)}${entryHint}`;
            ctx.font = isSecondLevel ? 'bold 9px Arial' : '9px Arial';
            const textWidth = ctx.measureText(labelText).width;

            // 背景颜色
            const bgAlpha = isSecondLevel ? 0.8 : 0.7;
            ctx.fillStyle = `rgba(0, 0, 0, ${bgAlpha})`;
            ctx.fillRect(22, y - 12, textWidth + 4, 10);

            // 绘制标签文字
            ctx.fillStyle = isSecondLevel ? `rgba(0, 255, 0, 1)` : `rgba(0, 255, 0, ${alpha + 0.2})`;
            ctx.textAlign = 'left';
            ctx.fillText(labelText, 24, y - 4);
        }
    });

    // 绘制压力位（红色虚线）
    resistanceLevels.forEach((level, index) => {
        if (level.price >= minPrice && level.price <= maxPrice) {
            const y = chartBottom - ((level.price - minPrice) / priceRange) * chartHeight;

            // 第二压力位特殊标记（右侧交易重点关注）
            const isSecondLevel = level.rank === 2;
            const baseAlpha = Math.min(0.8, 0.4 + (level.strength * level.touches) * 0.1);
            const alpha = isSecondLevel ? Math.min(0.9, baseAlpha + 0.2) : baseAlpha;

            // 设置虚线样式
            if (isSecondLevel) {
                ctx.setLineDash([10, 3, 2, 3]); // 特殊虚线样式
            } else {
                ctx.setLineDash([8, 4]);
            }

            ctx.strokeStyle = `rgba(255, 0, 0, ${alpha})`;
            ctx.lineWidth = isSecondLevel ? Math.max(2, level.touches * 0.6) : Math.max(1, level.touches * 0.5);

            // 绘制水平线
            ctx.beginPath();
            ctx.moveTo(20, y);
            ctx.lineTo(width - 20, y);
            ctx.stroke();

            // 绘制标签背景
            const intervalNames = { '1m': '1分', '5m': '5分', '15m': '15分', '1h': '1时', '4h': '4时', '1d': '1日' };
            const intervalName = intervalNames[currentInterval] || currentInterval;
            const entryHint = isSecondLevel ? ' 🎯进场' : '';
            const labelText = `${intervalName}${level.label} ${level.price.toFixed(2)}${entryHint}`;
            ctx.font = isSecondLevel ? 'bold 9px Arial' : '9px Arial';
            const textWidth = ctx.measureText(labelText).width;

            // 背景颜色
            const bgAlpha = isSecondLevel ? 0.8 : 0.7;
            ctx.fillStyle = `rgba(0, 0, 0, ${bgAlpha})`;
            ctx.fillRect(22, y + 2, textWidth + 4, 10);

            // 绘制标签文字
            ctx.fillStyle = isSecondLevel ? `rgba(255, 0, 0, 1)` : `rgba(255, 0, 0, ${alpha + 0.2})`;
            ctx.textAlign = 'left';
            ctx.fillText(labelText, 24, y + 10);
        }
    });

    // 重置线条样式
    ctx.setLineDash([]);
}

// 绘制缩放信息
function drawZoomInfo(ctx, width, height) {
    ctx.fillStyle = KLINE_CONFIG.colors.text;
    ctx.font = '9px Arial';
    ctx.textAlign = 'left';

    // 获取周期中文名称
    const intervalNames = {
        '1m': '1分钟',
        '5m': '5分钟',
        '15m': '15分钟',
        '1h': '1小时',
        '4h': '4小时',
        '1d': '1天'
    };

    const intervalName = intervalNames[currentInterval] || currentInterval;

    // 显示滚动位置信息
    const scrollInfo = scrollOffset > 0 ? ` | 历史偏移: ${scrollOffset}` : '';

    if (showSupportResistance && (supportLevels.length > 0 || resistanceLevels.length > 0)) {
        // 获取第二位置信息
        const secondSupport = supportLevels.find(l => l.rank === 2);
        const secondResistance = resistanceLevels.find(l => l.rank === 2);

        let entryInfo = '';
        if (secondSupport && secondResistance) {
            entryInfo = ` | 🎯进场区间: ${secondSupport.price.toFixed(2)}-${secondResistance.price.toFixed(2)}`;
        } else if (secondSupport) {
            entryInfo = ` | 🎯第二支撑: ${secondSupport.price.toFixed(2)}`;
        } else if (secondResistance) {
            entryInfo = ` | 🎯第二压力: ${secondResistance.price.toFixed(2)}`;
        }

        const srInfo = `| ${intervalName}: 支撑${supportLevels.length} 压力${resistanceLevels.length}${entryInfo}`;
        const info = `缩放: ${zoomLevel.toFixed(1)}x | 显示: ${visibleCandles}根K线${scrollInfo} ${srInfo}`;
        ctx.fillText(info, 5, height - 5);
    } else {
        const info = `缩放: ${zoomLevel.toFixed(1)}x | 显示: ${visibleCandles}根K线${scrollInfo}`;
        ctx.fillText(info, 5, height - 5);
    }
}

// 处理鼠标滚轮事件
function handleMouseWheel(event) {
    event.preventDefault();

    const delta = event.deltaY;
    if (delta < 0) {
        // 向上滚动 - 放大
        zoomLevel = Math.min(zoomLevel * 1.1, 3.0);
    } else {
        // 向下滚动 - 缩小
        zoomLevel = Math.max(zoomLevel / 1.1, 0.3);
    }

    updateZoomSettings();
    drawKlineChart();

    console.log('🖱️ 滚轮缩放:', zoomLevel.toFixed(2));
}

// 处理鼠标按下事件
function handleMouseDown(event) {
    if (event.target === chartCanvas) {
        isDragging = true;
        dragStartX = event.clientX;
        dragStartOffset = scrollOffset;
        chartCanvas.style.cursor = 'grabbing';
        event.preventDefault();
        console.log('🖱️ 开始拖拽');
    }
}

// 处理鼠标移动事件
function handleMouseMove(event) {
    if (!isDragging) {
        return;
    }

    const deltaX = event.clientX - dragStartX;
    const sensitivity = 2.0; // 增加拖拽敏感度
    const candleMove = Math.floor(deltaX * sensitivity / (candleSpacing || 10));
    const newOffset = dragStartOffset - candleMove;

    // 限制滚动范围
    const maxOffset = Math.max(0, klineData.length - visibleCandles);
    scrollOffset = Math.max(0, Math.min(newOffset, maxOffset));

    drawKlineChart();
    event.preventDefault();
    console.log('🖱️ 拖拽中，偏移:', scrollOffset);
}

// 处理鼠标释放事件
function handleMouseUp(event) {
    if (isDragging) {
        isDragging = false;
        chartCanvas.style.cursor = 'grab';
        console.log('🖱️ 拖拽结束，最终偏移:', scrollOffset);
    }
}

// 处理键盘事件
function handleKeyDown(event) {
    const step = 5; // 每次移动的K线数量
    const maxOffset = Math.max(0, klineData.length - visibleCandles);

    switch(event.key) {
        case 'ArrowLeft':
            // 向左滚动（查看更早的数据）
            scrollOffset = Math.min(scrollOffset + step, maxOffset);
            drawKlineChart();
            event.preventDefault();
            console.log('⬅️ 向左滚动:', scrollOffset);
            break;

        case 'ArrowRight':
            // 向右滚动（查看更新的数据）
            scrollOffset = Math.max(scrollOffset - step, 0);
            drawKlineChart();
            event.preventDefault();
            console.log('➡️ 向右滚动:', scrollOffset);
            break;

        case 'Home':
            // 跳到最开始
            scrollOffset = maxOffset;
            drawKlineChart();
            event.preventDefault();
            console.log('🏠 跳到开始');
            break;

        case 'End':
            // 跳到最新
            scrollOffset = 0;
            drawKlineChart();
            event.preventDefault();
            console.log('🔚 跳到最新');
            break;
    }
}

// 计算支撑位和压力位
function calculateSupportResistance(data) {
    if (!data || data.length < 20) return;

    supportLevels = [];
    resistanceLevels = [];

    // 根据不同周期调整参数
    const intervalParams = getIntervalParams(currentInterval);

    // 寻找局部高点和低点
    const localHighs = [];
    const localLows = [];
    const lookback = Math.max(intervalParams.minLookback, Math.floor(data.length / intervalParams.lookbackRatio));

    for (let i = lookback; i < data.length - lookback; i++) {
        const current = data[i];
        let isLocalHigh = true;
        let isLocalLow = true;

        // 检查是否为局部高点
        for (let j = i - lookback; j <= i + lookback; j++) {
            if (j !== i && data[j].high >= current.high) {
                isLocalHigh = false;
                break;
            }
        }

        // 检查是否为局部低点
        for (let j = i - lookback; j <= i + lookback; j++) {
            if (j !== i && data[j].low <= current.low) {
                isLocalLow = false;
                break;
            }
        }

        if (isLocalHigh) {
            // 计算高点的重要性（基于成交量和价格位置）
            const importance = calculateImportance(data, i, 'high');
            localHighs.push({
                price: current.high,
                index: i,
                strength: importance,
                volume: current.volume
            });
        }

        if (isLocalLow) {
            // 计算低点的重要性
            const importance = calculateImportance(data, i, 'low');
            localLows.push({
                price: current.low,
                index: i,
                strength: importance,
                volume: current.volume
            });
        }
    }

    // 合并相近的价位
    const priceThreshold = intervalParams.priceThreshold;

    // 处理阻力位（局部高点）
    const groupedHighs = groupNearbyLevels(localHighs, priceThreshold);
    resistanceLevels = groupedHighs
        .filter(level => level.touches >= intervalParams.minTouches)
        .sort((a, b) => (b.strength * b.touches) - (a.strength * a.touches))
        .slice(0, intervalParams.maxLevels);

    // 处理支撑位（局部低点）
    const groupedLows = groupNearbyLevels(localLows, priceThreshold);
    supportLevels = groupedLows
        .filter(level => level.touches >= intervalParams.minTouches)
        .sort((a, b) => (b.strength * b.touches) - (a.strength * a.touches))
        .slice(0, intervalParams.maxLevels);

    // 为支撑位和压力位添加排序标签
    supportLevels.forEach((level, index) => {
        level.rank = index + 1; // 第一支撑位、第二支撑位等
        level.label = `第${['一', '二', '三', '四'][index] || (index + 1)}支撑位`;
    });

    resistanceLevels.forEach((level, index) => {
        level.rank = index + 1; // 第一压力位、第二压力位等
        level.label = `第${['一', '二', '三', '四'][index] || (index + 1)}压力位`;
    });

    console.log('📊 计算支撑压力位:', {
        周期: currentInterval,
        支撑位: supportLevels.map(l => `${l.label}: ${l.price.toFixed(2)}`),
        压力位: resistanceLevels.map(l => `${l.label}: ${l.price.toFixed(2)}`),
        数据长度: data.length,
        回看周期: lookback
    });
}

// 根据时间周期获取计算参数
function getIntervalParams(interval) {
    const params = {
        '1m': {
            minLookback: 2,           // 最小回看周期
            lookbackRatio: 40,        // 数据长度除数
            priceThreshold: 0.0008,   // 0.08% 价格阈值（短期更敏感）
            minTouches: 2,            // 最少测试次数
            maxLevels: 2              // 只显示第一、第二位
        },
        '5m': {
            minLookback: 3,
            lookbackRatio: 35,
            priceThreshold: 0.0015,   // 0.15%
            minTouches: 2,
            maxLevels: 2
        },
        '15m': {
            minLookback: 3,
            lookbackRatio: 30,
            priceThreshold: 0.0025,   // 0.25%
            minTouches: 2,
            maxLevels: 2
        },
        '1h': {
            minLookback: 4,
            lookbackRatio: 25,
            priceThreshold: 0.004,    // 0.4%
            minTouches: 2,
            maxLevels: 2
        },
        '4h': {
            minLookback: 3,           // 降低回看周期
            lookbackRatio: 30,        // 增加比例
            priceThreshold: 0.008,    // 0.8% 更宽松
            minTouches: 1,            // 降低最少测试次数
            maxLevels: 2
        },
        '1d': {
            minLookback: 2,           // 进一步降低
            lookbackRatio: 35,        // 进一步增加比例
            priceThreshold: 0.015,    // 1.5% 更宽松
            minTouches: 1,            // 降低最少测试次数
            maxLevels: 2
        }
    };

    return params[interval] || params['1h']; // 默认使用1小时参数
}

// 更新交易提示
function updateTradingHint() {
    const hintElement = document.getElementById('trading-hint');
    const contentElement = document.getElementById('hint-content');

    if (!hintElement || !contentElement) return;

    if (!showSupportResistance || (supportLevels.length === 0 && resistanceLevels.length === 0)) {
        hintElement.style.display = 'none';
        return;
    }

    const currentPrice = klineData.length > 0 ? klineData[klineData.length - 1].close : 0;
    const secondSupport = supportLevels.find(l => l.rank === 2);
    const secondResistance = resistanceLevels.find(l => l.rank === 2);

    let hintText = '';
    let showHint = false;

    // 获取周期名称
    const intervalNames = { '1m': '1分', '5m': '5分', '15m': '15分', '1h': '1时', '4h': '4时', '1d': '1日' };
    const intervalName = intervalNames[currentInterval] || currentInterval;

    if (secondSupport && secondResistance) {
        const supportDistance = Math.abs(currentPrice - secondSupport.price) / currentPrice * 100;
        const resistanceDistance = Math.abs(currentPrice - secondResistance.price) / currentPrice * 100;

        if (supportDistance <= 0.5) {
            // 接近第二支撑位
            hintText = `${intervalName}周期：价格接近第二支撑位 ${secondSupport.price.toFixed(2)}\n🎯 右侧交易：等待突破确认后做多\n⚠️ 风险控制：跌破支撑位止损`;
            showHint = true;
        } else if (resistanceDistance <= 0.5) {
            // 接近第二压力位
            hintText = `${intervalName}周期：价格接近第二压力位 ${secondResistance.price.toFixed(2)}\n🎯 右侧交易：等待突破确认后做空\n⚠️ 风险控制：突破压力位止损`;
            showHint = true;
        } else if (currentPrice > secondSupport.price && currentPrice < secondResistance.price) {
            // 在区间内
            hintText = `${intervalName}周期：价格在区间内震荡\n📊 支撑: ${secondSupport.price.toFixed(2)} | 压力: ${secondResistance.price.toFixed(2)}\n🎯 等待突破第二位置确认方向`;
            showHint = true;
        }
    } else if (secondSupport) {
        const supportDistance = Math.abs(currentPrice - secondSupport.price) / currentPrice * 100;
        if (supportDistance <= 0.8) {
            hintText = `${intervalName}周期：关注第二支撑位 ${secondSupport.price.toFixed(2)}\n🎯 右侧交易：等待突破确认`;
            showHint = true;
        }
    } else if (secondResistance) {
        const resistanceDistance = Math.abs(currentPrice - secondResistance.price) / currentPrice * 100;
        if (resistanceDistance <= 0.8) {
            hintText = `${intervalName}周期：关注第二压力位 ${secondResistance.price.toFixed(2)}\n🎯 右侧交易：等待突破确认`;
            showHint = true;
        }
    }

    if (showHint) {
        contentElement.textContent = hintText;
        hintElement.style.display = 'block';
    } else {
        hintElement.style.display = 'none';
    }
}

// 计算价位的重要性
function calculateImportance(data, index, type) {
    const current = data[index];
    let importance = 1;

    // 基于成交量的重要性
    const avgVolume = data.slice(Math.max(0, index - 10), index + 10)
        .reduce((sum, k) => sum + k.volume, 0) / 20;
    if (current.volume > avgVolume * 1.5) {
        importance += 0.5; // 高成交量增加重要性
    }

    // 基于价格位置的重要性
    const recentData = data.slice(Math.max(0, index - 50), index + 50);
    const prices = recentData.flatMap(k => [k.high, k.low]);
    const maxPrice = Math.max(...prices);
    const minPrice = Math.min(...prices);
    const priceRange = maxPrice - minPrice;

    if (type === 'high') {
        const pricePosition = (current.high - minPrice) / priceRange;
        if (pricePosition > 0.8) importance += 0.3; // 接近高位
    } else {
        const pricePosition = (current.low - minPrice) / priceRange;
        if (pricePosition < 0.2) importance += 0.3; // 接近低位
    }

    return importance;
}

// 合并相近的价位
function groupNearbyLevels(levels, threshold) {
    if (levels.length === 0) return [];

    const grouped = [];
    const sorted = levels.sort((a, b) => a.price - b.price);

    let currentGroup = [sorted[0]];

    for (let i = 1; i < sorted.length; i++) {
        const current = sorted[i];
        const groupAvg = currentGroup.reduce((sum, item) => sum + item.price, 0) / currentGroup.length;

        if (Math.abs(current.price - groupAvg) / groupAvg <= threshold) {
            // 价位相近，加入当前组
            currentGroup.push(current);
        } else {
            // 价位差异较大，创建新组
            if (currentGroup.length > 0) {
                const avgPrice = currentGroup.reduce((sum, item) => sum + item.price, 0) / currentGroup.length;
                const totalStrength = currentGroup.length;
                grouped.push({
                    price: avgPrice,
                    strength: totalStrength,
                    touches: currentGroup.length
                });
            }
            currentGroup = [current];
        }
    }

    // 处理最后一组
    if (currentGroup.length > 0) {
        const avgPrice = currentGroup.reduce((sum, item) => sum + item.price, 0) / currentGroup.length;
        const totalStrength = currentGroup.length;
        grouped.push({
            price: avgPrice,
            strength: totalStrength,
            touches: currentGroup.length
        });
    }

    return grouped;
}

// 更新图表统计信息
function updateChartStats() {
    if (!klineData.length) return;
    
    const latestKline = klineData[klineData.length - 1];
    
    document.getElementById('kline-open').textContent = `$${latestKline.open.toFixed(2)}`;
    document.getElementById('kline-high').textContent = `$${latestKline.high.toFixed(2)}`;
    document.getElementById('kline-low').textContent = `$${latestKline.low.toFixed(2)}`;
    document.getElementById('kline-close').textContent = `$${latestKline.close.toFixed(2)}`;
    document.getElementById('kline-volume').textContent = formatVolume(latestKline.volume);
}

// 格式化成交量
function formatVolume(volume) {
    if (volume >= 1000000) {
        return (volume / 1000000).toFixed(1) + 'M';
    } else if (volume >= 1000) {
        return (volume / 1000).toFixed(1) + 'K';
    } else {
        return volume.toFixed(2);
    }
}

// 显示/隐藏加载状态
function showLoading(show) {
    const loadingElement = document.getElementById('chart-loading');
    if (loadingElement) {
        loadingElement.style.display = show ? 'flex' : 'none';
    }
}

// 显示错误信息
function showError(message) {
    console.error('📈 K线图表错误:', message);
    // 可以在这里添加错误提示UI
}

// 启动自动更新
function startAutoUpdate() {
    if (updateTimer) {
        clearInterval(updateTimer);
    }
    
    updateTimer = setInterval(() => {
        if (!isLoading) {
            loadKlineData(currentInterval);
        }
    }, KLINE_CONFIG.updateInterval);
    
    console.log(`⏰ K线图表自动更新已启动，间隔${KLINE_CONFIG.updateInterval/1000}秒`);
}

// 停止自动更新
function stopAutoUpdate() {
    if (updateTimer) {
        clearInterval(updateTimer);
        updateTimer = null;
        console.log('⏸️ K线图表自动更新已停止');
    }
}

// 导出函数到全局作用域
window.initializeKlineChart = initializeKlineChart;
window.loadKlineData = loadKlineData;
window.stopAutoUpdate = stopAutoUpdate;

console.log('📈 BTC K线图表模块已加载');
