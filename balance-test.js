// 现货余额修复验证脚本
// 在浏览器控制台中运行此脚本来验证修复效果

console.log('🧪 开始现货余额修复验证...');

// 1. 检查数据状态
function checkBalanceData() {
    console.log('\n📊 === 数据状态检查 ===');
    
    // 检查API状态
    console.log('🔗 API状态:', window.apiStatus);
    
    // 检查现货账户数据
    console.log('💰 现货账户数据:', window.spotAccountBalance);
    if (window.spotAccountBalance) {
        console.log(`   总余额: $${window.spotAccountBalance.totalBalance?.toFixed(2) || '0.00'}`);
        console.log(`   可用余额: $${window.spotAccountBalance.availableBalance?.toFixed(2) || '0.00'}`);
        console.log(`   冻结余额: $${window.spotAccountBalance.frozenBalance?.toFixed(2) || '0.00'}`);
    }
    
    // 检查杠杆账户数据
    console.log('⚡ 杠杆账户数据:', window.marginAccountBalance);
    if (window.marginAccountBalance) {
        console.log(`   净资产: $${window.marginAccountBalance.netAsset?.toFixed(2) || '0.00'}`);
        console.log(`   总资产: $${window.marginAccountBalance.totalAsset?.toFixed(2) || '0.00'}`);
    }
    
    // 检查关键函数
    const functions = ['switchAccountType', 'updateBalanceDisplay', 'fetchSpotAccountInfo'];
    console.log('\n🔧 函数检查:');
    functions.forEach(funcName => {
        const exists = typeof window[funcName] === 'function';
        console.log(`   ${funcName}: ${exists ? '✅ 已加载' : '❌ 未加载'}`);
    });
}

// 2. 模拟账户数据（用于测试）
function simulateBalanceData() {
    console.log('\n🎭 === 设置模拟数据 ===');
    
    // 模拟现货账户数据
    window.spotAccountBalance = {
        totalBalance: 1234.56,
        availableBalance: 1000.00,
        frozenBalance: 234.56,
        dailyPnL: 45.67,
        dailyChangePercent: 3.2
    };
    
    // 模拟杠杆账户数据
    window.marginAccountBalance = {
        netAsset: 2345.67,
        totalAsset: 2500.00,
        totalLiability: 154.33,
        dailyPnL: -12.34,
        dailyChangePercent: -0.5
    };
    
    // 模拟API连接状态
    if (!window.apiStatus) {
        window.apiStatus = {};
    }
    window.apiStatus.connected = true;
    
    console.log('✅ 模拟数据设置完成');
    console.log('💰 现货总余额:', window.spotAccountBalance.totalBalance);
    console.log('⚡ 杠杆净资产:', window.marginAccountBalance.netAsset);
}

// 3. 测试账户切换功能
function testAccountSwitch() {
    console.log('\n🔄 === 测试账户切换 ===');
    
    if (typeof window.switchAccountType !== 'function') {
        console.error('❌ switchAccountType函数未找到');
        return;
    }
    
    // 获取当前显示的余额
    const currentBalance = document.getElementById('currentTotalBalance');
    if (!currentBalance) {
        console.error('❌ currentTotalBalance元素未找到');
        return;
    }
    
    console.log('🔄 切换到现货账户...');
    window.switchAccountType('spot');
    setTimeout(() => {
        console.log(`💰 现货账户余额显示: ${currentBalance.textContent}`);
        
        console.log('🔄 切换到杠杆账户...');
        window.switchAccountType('margin');
        setTimeout(() => {
            console.log(`⚡ 杠杆账户余额显示: ${currentBalance.textContent}`);
            
            console.log('🔄 切换回现货账户...');
            window.switchAccountType('spot');
            setTimeout(() => {
                console.log(`💰 现货账户余额显示: ${currentBalance.textContent}`);
                console.log('✅ 账户切换测试完成');
            }, 500);
        }, 500);
    }, 500);
}

// 4. 检查DOM元素状态
function checkDOMElements() {
    console.log('\n🎯 === DOM元素检查 ===');
    
    const elements = [
        'spotTotalBalance',
        'spotAvailableBalance', 
        'marginNetAsset',
        'currentTotalBalance',
        'spotAccountTab',
        'marginAccountTab'
    ];
    
    elements.forEach(elementId => {
        const element = document.getElementById(elementId);
        if (element) {
            console.log(`✅ ${elementId}: ${element.textContent || '(空)'}`);
        } else {
            console.log(`❌ ${elementId}: 未找到`);
        }
    });
}

// 5. 强制更新余额显示
function forceUpdateBalance() {
    console.log('\n🔄 === 强制更新余额显示 ===');
    
    if (typeof window.updateBalanceDisplay === 'function') {
        console.log('🔄 调用updateBalanceDisplay...');
        window.updateBalanceDisplay();
        console.log('✅ 余额显示更新完成');
    } else {
        console.error('❌ updateBalanceDisplay函数未找到');
    }
    
    // 强制切换到现货账户
    if (typeof window.switchAccountType === 'function') {
        console.log('🔄 强制切换到现货账户...');
        window.switchAccountType('spot');
        console.log('✅ 现货账户切换完成');
    }
}

// 主测试函数
function runBalanceTest() {
    console.log('🚀 === 现货余额修复验证开始 ===');
    
    // 步骤1: 检查当前状态
    checkBalanceData();
    
    // 步骤2: 检查DOM元素
    checkDOMElements();
    
    // 如果没有真实数据，使用模拟数据
    if (!window.spotAccountBalance) {
        console.log('\n⚠️ 未检测到真实账户数据，使用模拟数据进行测试...');
        simulateBalanceData();
        
        // 更新显示
        setTimeout(() => {
            forceUpdateBalance();
            
            // 测试切换功能
            setTimeout(() => {
                testAccountSwitch();
            }, 1000);
        }, 500);
    } else {
        console.log('\n✅ 检测到真实账户数据');
        
        // 强制更新显示
        forceUpdateBalance();
        
        // 测试切换功能
        setTimeout(() => {
            testAccountSwitch();
        }, 1000);
    }
    
    console.log('\n📋 === 验证完成，请查看上述输出结果 ===');
    console.log('💡 如果现货余额显示正确，说明修复成功！');
}

// 导出测试函数
window.runBalanceTest = runBalanceTest;
window.checkBalanceData = checkBalanceData;
window.simulateBalanceData = simulateBalanceData;
window.testAccountSwitch = testAccountSwitch;

// 自动运行测试（延迟3秒确保页面完全加载）
setTimeout(() => {
    console.log('🎯 自动运行现货余额修复验证...');
    runBalanceTest();
}, 3000);

console.log('📝 验证脚本已加载，3秒后自动运行测试');
console.log('💡 您也可以手动运行: runBalanceTest()');
