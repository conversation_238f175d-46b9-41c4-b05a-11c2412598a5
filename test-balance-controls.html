<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>余额控制按钮测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            background: #0a0a0a;
            color: #fff;
            font-family: Arial, sans-serif;
        }
        
        .test-container {
            max-width: 600px;
            margin: 0 auto;
        }
        
        .demo-section {
            background: #222;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
        }
        
        .btn {
            background: #00d4aa;
            color: #000;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin: 5px;
            font-weight: bold;
        }
        
        .btn:hover {
            background: #00b894;
        }
        
        /* 模拟主页面的余额卡片样式 */
        .balance-card {
            background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
            border-radius: 12px;
            padding: 20px;
            margin: 20px 0;
            border: 1px solid #374151;
        }
        
        .balance-header-top {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        
        .account-switch-tabs {
            display: flex;
            gap: 8px;
        }
        
        .account-tab {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .account-tab.active {
            background: #00d4aa;
            color: #000;
            border-color: #00d4aa;
        }
        
        .balance-controls {
            display: flex;
            gap: 6px;
            align-items: center;
        }
        
        .balance-control-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: #fff;
            width: 32px;
            height: 32px;
            border-radius: 6px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            transition: all 0.3s ease;
        }
        
        .balance-control-btn:hover {
            background: rgba(0, 212, 170, 0.2);
            border-color: #00d4aa;
            transform: translateY(-1px);
        }
        
        .balance-control-btn.refreshing {
            animation: spin 1s linear infinite;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .balance-amount {
            font-size: 24px;
            font-weight: bold;
            color: #00d4aa;
            margin: 10px 0;
        }
        
        .balance-item {
            display: flex;
            justify-content: space-between;
            margin: 8px 0;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .balance-item-value {
            color: #10b981;
            font-weight: bold;
        }
        
        /* 隐藏余额状态 */
        .balance-hidden .balance-amount,
        .balance-hidden .balance-item-value {
            color: transparent;
            text-shadow: 0 0 8px rgba(255, 255, 255, 0.5);
            user-select: none;
            position: relative;
        }
        
        .balance-hidden .balance-amount::after,
        .balance-hidden .balance-item-value::after {
            content: '****';
            color: #888;
            text-shadow: none;
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            top: 0;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1>👁️ 余额控制按钮测试</h1>
        
        <div class="demo-section">
            <h2>功能说明</h2>
            <p>✅ <strong>眼睛按钮</strong>：点击可以隐藏/显示所有余额金额</p>
            <p>✅ <strong>刷新按钮</strong>：点击可以刷新账户余额，带旋转动画</p>
            <p>✅ <strong>状态记忆</strong>：隐藏状态会保存到本地存储</p>
        </div>
        
        <div class="demo-section">
            <h2>模拟余额卡片</h2>
            
            <div class="balance-card" id="demoBalanceCard">
                <div class="balance-header-top">
                    <div class="account-switch-tabs">
                        <button class="account-tab active">🔵 现货</button>
                        <button class="account-tab">🔴 杠杆</button>
                    </div>
                    <div class="balance-controls">
                        <button class="balance-control-btn" onclick="toggleDemoVisibility()" title="隐藏/显示金额">
                            👁️
                        </button>
                        <button class="balance-control-btn" onclick="refreshDemo()" title="刷新账户余额">
                            🔄
                        </button>
                    </div>
                </div>
                
                <div class="balance-amount">$1,234.56</div>
                
                <div class="balance-item">
                    <span>可用余额 (USDT)</span>
                    <span class="balance-item-value">$1,000.00</span>
                </div>
                <div class="balance-item">
                    <span>冻结资金</span>
                    <span class="balance-item-value">$234.56</span>
                </div>
                <div class="balance-item">
                    <span>今日盈亏</span>
                    <span class="balance-item-value">+$45.67</span>
                </div>
            </div>
            
            <button class="btn" onclick="location.href='/'">返回主页测试</button>
        </div>
    </div>

    <script>
        let demoVisible = true;
        
        // 切换演示余额可见性
        function toggleDemoVisibility() {
            const card = document.getElementById('demoBalanceCard');
            const btn = event.target;
            
            demoVisible = !demoVisible;
            
            if (demoVisible) {
                card.classList.remove('balance-hidden');
                btn.textContent = '👁️';
                btn.title = '隐藏金额';
                console.log('👁️ 演示余额已显示');
            } else {
                card.classList.add('balance-hidden');
                btn.textContent = '🙈';
                btn.title = '显示金额';
                console.log('🙈 演示余额已隐藏');
            }
        }
        
        // 刷新演示
        function refreshDemo() {
            const btn = event.target;
            
            // 添加旋转动画
            btn.classList.add('refreshing');
            btn.title = '正在刷新...';
            
            console.log('🔄 演示刷新开始...');
            
            // 模拟刷新过程
            setTimeout(() => {
                // 更新余额数据
                const amount = document.querySelector('.balance-amount');
                const values = document.querySelectorAll('.balance-item-value');
                
                // 生成随机余额
                const newTotal = (Math.random() * 2000 + 500).toFixed(2);
                amount.textContent = `$${newTotal}`;
                
                values[0].textContent = `$${(newTotal * 0.8).toFixed(2)}`;
                values[1].textContent = `$${(newTotal * 0.2).toFixed(2)}`;
                values[2].textContent = `${Math.random() > 0.5 ? '+' : '-'}$${(Math.random() * 100).toFixed(2)}`;
                
                console.log('✅ 演示刷新完成');
                
                // 显示成功状态
                btn.textContent = '✅';
                btn.style.background = 'rgba(0, 212, 170, 0.3)';
                
                setTimeout(() => {
                    btn.classList.remove('refreshing');
                    btn.textContent = '🔄';
                    btn.style.background = '';
                    btn.title = '刷新账户余额';
                }, 1000);
                
            }, 2000);
        }
        
        console.log('🎯 余额控制按钮测试页面已加载');
        console.log('💡 请测试眼睛按钮和刷新按钮的功能');
    </script>
</body>
</html>
