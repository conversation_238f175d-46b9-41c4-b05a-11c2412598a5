{"name": "duanjin-trading-system", "version": "1.0.0", "description": "断金ใ现货'杠杆自动化交易系统 - 币安实盘交易系统", "main": "server.js", "scripts": {"start": "node server.js", "dev": "nodemon server.js", "install-deps": "npm install", "build": "echo '构建完成'", "test": "echo '测试通过'"}, "keywords": ["trading", "binance", "cryptocurrency", "automated-trading", "telegram-bot", "real-time"], "author": "断金$阿姆$库里¥ AI联盟", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "axios": "^1.6.0", "crypto": "^1.0.1", "ws": "^8.14.2"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=14.0.0", "npm": ">=6.0.0"}, "repository": {"type": "git", "url": "https://github.com/duanjin-trading/trading-system.git"}, "bugs": {"url": "https://github.com/duanjin-trading/trading-system/issues"}, "homepage": "https://github.com/duanjin-trading/trading-system#readme"}