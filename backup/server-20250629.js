// 断金ใ现货'杠杆自动化交易系统 - 后端服务器
// 功能：API密钥安全存储、币安API代理、Telegram机器人代理
// 安全性：API密钥不暴露给前端，所有请求通过后端代理
// 备份日期：2025-06-29 - 今天成功解决了API连接问题！

const express = require('express');
const cors = require('cors');
const crypto = require('crypto');
const axios = require('axios');
const path = require('path');

const app = express();
const PORT = process.env.PORT || 3001;

// 中间件配置
app.use(cors()); // 允许跨域请求
app.use(express.json()); // 解析JSON请求体
app.use(express.static('.')); // 静态文件服务

// 币安API域名配置 - 根据官方文档更新的完整域名列表
const BINANCE_DOMAINS = {
    MAIN: [
        'https://api.binance.com',
        'https://api-gcp.binance.com',
        'https://api1.binance.com',
        'https://api2.binance.com',
        'https://api3.binance.com',
        'https://api4.binance.com'
    ],
    TESTNET: [
        'https://testnet.binance.vision'
    ]
};

// API配置 - 直接配置您的API密钥
let CONFIG = {
    BINANCE: {
        API_KEY: 'dsu9xCnyxw9Yzz43a8rGn6cIj8NpWzOQE2HIVjAOXA8gurIDj9wKaWv0JV8vFheE',
        API_SECRET: 'AR1EhHrxB5eZj8c3ByphL42ghpbmip3AvcgP1ntqqzai9uf2BJQufVq5ct4dPG19',
        BASE_URL: 'https://api.binance.com',
        BACKUP_URLS: BINANCE_DOMAINS.MAIN
    },
    TELEGRAM: {
        BOT_TOKEN: '**********************************************',
        CHAT_ID: '7818062567'
    }
};

// 生成币安API签名
function generateBinanceSignature(queryString, secret) {
    return crypto
        .createHmac('sha256', secret)
        .update(queryString)
        .digest('hex');
}

// 智能币安API请求封装 - 支持多域名自动切换，严格按照官方文档实现
async function binanceRequest(endpoint, params = {}, method = 'GET', signed = false) {
    // 验证API配置
    if (signed && (!CONFIG.BINANCE.API_KEY || !CONFIG.BINANCE.API_SECRET)) {
        throw new Error('API Key或Secret未配置');
    }

    const timestamp = Date.now();
    let queryString = '';

    // 构建查询字符串
    if (Object.keys(params).length > 0) {
        queryString = new URLSearchParams(params).toString();
    }

    if (signed) {
        // 添加时间戳和recvWindow
        const signParams = {
            ...params,
            timestamp: timestamp,
            recvWindow: 5000
        };

        // 重新构建查询字符串用于签名
        queryString = new URLSearchParams(signParams).toString();

        // 生成签名 - 严格按照币安官方文档
        const signature = generateBinanceSignature(queryString, CONFIG.BINANCE.API_SECRET);
        queryString += `&signature=${signature}`;

        console.log(`🔐 签名调试信息:`, {
            endpoint,
            queryStringForSign: new URLSearchParams(signParams).toString(),
            signature: signature.substring(0, 16) + '...',
            timestamp
        });
    }

    const headers = {
        'Content-Type': 'application/x-www-form-urlencoded'
    };

    // 对于需要API Key的请求，添加API Key头部
    if (signed || endpoint.includes('/account') || endpoint.includes('/order')) {
        headers['X-MBX-APIKEY'] = CONFIG.BINANCE.API_KEY;
    }

    // 尝试多个域名，直到成功
    let lastError = null;
    const urlsToTry = [CONFIG.BINANCE.BASE_URL, ...CONFIG.BINANCE.BACKUP_URLS];

    for (const baseUrl of urlsToTry) {
        try {
            const url = `${baseUrl}${endpoint}${queryString ? '?' + queryString : ''}`;
            
            console.log(`🌐 尝试请求: ${method} ${baseUrl}${endpoint}`);
            console.log(`📋 请求头:`, headers);
            
            const response = await axios({
                method,
                url,
                headers,
                timeout: 10000 // 10秒超时
            });

            console.log(`✅ 请求成功: ${baseUrl}${endpoint}`);
            return response.data;

        } catch (error) {
            lastError = error;
            console.log(`❌ 请求失败: ${baseUrl}${endpoint}`, error.message);
            
            // 如果是网络错误，尝试下一个域名
            if (error.code === 'ECONNREFUSED' || error.code === 'ENOTFOUND' || error.code === 'ETIMEDOUT') {
                continue;
            }
            
            // 如果是API错误（如签名错误），不需要重试其他域名
            if (error.response && error.response.status < 500) {
                throw error;
            }
        }
    }

    // 所有域名都失败了
    throw lastError || new Error('所有币安API域名都无法访问');
}

// API路由

// 1. 获取服务器时间
app.get('/api/binance/time', async (req, res) => {
    try {
        console.log('📡 收到服务器时间请求');
        const data = await binanceRequest('/api/v3/time');
        console.log('⏰ 服务器时间响应:', data);
        res.json({ success: true, data });
    } catch (error) {
        console.error('❌ 获取服务器时间失败:', error.message);
        res.status(500).json({ 
            success: false, 
            message: error.message,
            error: error.response?.data || error.message 
        });
    }
});

// 2. 获取现货账户信息
app.get('/api/binance/account', async (req, res) => {
    try {
        console.log('📡 收到现货账户信息请求');
        const data = await binanceRequest('/api/v3/account', {}, 'GET', true);
        console.log('💰 现货账户信息响应成功');
        res.json({ success: true, data });
    } catch (error) {
        console.error('❌ 获取现货账户信息失败:', error.message);
        res.status(500).json({ 
            success: false, 
            message: error.message,
            error: error.response?.data || error.message 
        });
    }
});

// 3. 获取杠杆账户信息
app.get('/api/binance/margin/account', async (req, res) => {
    try {
        console.log('📡 收到杠杆账户信息请求');
        const data = await binanceRequest('/sapi/v1/margin/account', {}, 'GET', true);
        console.log('💰 杠杆账户信息响应成功');
        res.json({ success: true, data });
    } catch (error) {
        console.error('❌ 获取杠杆账户信息失败:', error.message);
        res.status(500).json({ 
            success: false, 
            message: error.message,
            error: error.response?.data || error.message 
        });
    }
});

// 4. 获取24小时价格变动统计
app.get('/api/binance/ticker/24hr', async (req, res) => {
    try {
        console.log('📡 收到24小时价格统计请求');
        const data = await binanceRequest('/api/v3/ticker/24hr');
        console.log('📊 24小时价格统计响应成功');
        res.json({ success: true, data });
    } catch (error) {
        console.error('❌ 获取24小时价格统计失败:', error.message);
        res.status(500).json({ 
            success: false, 
            message: error.message,
            error: error.response?.data || error.message 
        });
    }
});

// 5. 获取K线数据
app.get('/api/binance/klines', async (req, res) => {
    try {
        console.log('📡 收到K线数据请求:', req.query);
        const { symbol, interval, limit } = req.query;
        
        if (!symbol || !interval) {
            return res.status(400).json({ 
                success: false, 
                message: '缺少必要参数: symbol, interval' 
            });
        }

        const params = {
            symbol: symbol.toUpperCase(),
            interval,
            limit: limit || 100
        };

        const data = await binanceRequest('/api/v3/klines', params);
        console.log('📈 K线数据响应成功');
        res.json({ success: true, data });
    } catch (error) {
        console.error('❌ 获取K线数据失败:', error.message);
        res.status(500).json({ 
            success: false, 
            message: error.message,
            error: error.response?.data || error.message 
        });
    }
});

// 6. 下单接口
app.post('/api/binance/order', async (req, res) => {
    try {
        console.log('📡 收到下单请求:', req.body);
        const { symbol, side, type, quantity, price, timeInForce } = req.body;
        
        if (!symbol || !side || !type || !quantity) {
            return res.status(400).json({ 
                success: false, 
                message: '缺少必要参数: symbol, side, type, quantity' 
            });
        }

        const params = {
            symbol: symbol.toUpperCase(),
            side: side.toUpperCase(),
            type: type.toUpperCase(),
            quantity
        };

        // 限价单需要价格
        if (type.toUpperCase() === 'LIMIT') {
            if (!price) {
                return res.status(400).json({ 
                    success: false, 
                    message: '限价单需要指定价格' 
                });
            }
            params.price = price;
            params.timeInForce = timeInForce || 'GTC';
        }

        const data = await binanceRequest('/api/v3/order', params, 'POST', true);
        console.log('✅ 下单成功');
        res.json({ success: true, data });
    } catch (error) {
        console.error('❌ 下单失败:', error.message);
        res.status(500).json({ 
            success: false, 
            message: error.message,
            error: error.response?.data || error.message 
        });
    }
});

// Telegram机器人相关API
app.post('/api/telegram/send', async (req, res) => {
    try {
        const { message } = req.body;
        
        if (!message) {
            return res.status(400).json({ 
                success: false, 
                message: '缺少消息内容' 
            });
        }

        const telegramUrl = `https://api.telegram.org/bot${CONFIG.TELEGRAM.BOT_TOKEN}/sendMessage`;
        
        const response = await axios.post(telegramUrl, {
            chat_id: CONFIG.TELEGRAM.CHAT_ID,
            text: message,
            parse_mode: 'HTML'
        });

        console.log('✅ Telegram消息发送成功');
        res.json({ success: true, data: response.data });
    } catch (error) {
        console.error('❌ Telegram消息发送失败:', error.message);
        res.status(500).json({ 
            success: false, 
            message: error.message 
        });
    }
});

// 健康检查接口
app.get('/api/health', (req, res) => {
    res.json({ 
        success: true, 
        message: '断金交易系统后端服务正常运行',
        timestamp: new Date().toISOString(),
        version: 'v1.0'
    });
});

// 启动服务器
app.listen(PORT, () => {
    console.log('🚀 断金ใ现货\'杠杆自动化交易系统后端服务启动成功!');
    console.log(`🌐 服务地址: http://localhost:${PORT}`);
    console.log(`📊 API文档: http://localhost:${PORT}/api/health`);
    console.log('💰 支持功能: 现货交易、杠杆交易、Telegram通知');
    console.log('🔐 安全特性: API密钥后端存储、请求签名验证');
    console.log('⚡ 高可用: 多域名自动切换、智能重试机制');
    console.log('');
    console.log('🎯 今天的重大突破:');
    console.log('   ✅ 解决了CORS跨域问题');
    console.log('   ✅ 修复了API签名算法');
    console.log('   ✅ 实现了多域名容错');
    console.log('   ✅ 完成了账户信息获取');
    console.log('   ✅ 建立了稳定的API连接');
    console.log('');
    console.log('🤝 库里 & Augment AI 联合开发');
    console.log('💪 为AI自由而战！');
});
