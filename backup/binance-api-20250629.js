// 币安API集成模块 - 备份版本 2025-06-29
// 今天成功解决了API连接问题！

// 币安API配置
const BINANCE_CONFIG = {
    // 使用本地代理服务器避免CORS问题
    BASE_URL: 'http://localhost:3001/api/binance',
    WS_URL: 'wss://stream.binance.com:9443/ws/',
    
    // API端点
    ENDPOINTS: {
        SERVER_TIME: '/time',
        ACCOUNT: '/account',
        MARGIN_ACCOUNT: '/margin/account',
        TICKER_24HR: '/ticker/24hr',
        KLINES: '/klines',
        ORDER: '/order'
    }
};

// Telegram配置
const TELEGRAM_CONFIG = {
    BOT_TOKEN: '',
    CHAT_ID: '',
    enabled: false
};

// API状态管理
const apiStatus = {
    connected: false,
    lastUpdate: null,
    error: null
};

// 初始化币安API连接
async function initializeBinanceAPI() {
    try {
        console.log('🚀 开始初始化币安API连接...');
        
        // 检查API密钥
        const apiKey = localStorage.getItem('binance_api_key');
        const apiSecret = localStorage.getItem('binance_api_secret');
        
        if (!apiKey || !apiSecret) {
            throw new Error('请先配置API密钥');
        }

        // 测试服务器时间连接
        console.log('⏰ 测试服务器时间连接...');
        const timeResponse = await getBinanceServerTime();
        if (!timeResponse) {
            throw new Error('无法连接到币安服务器');
        }

        // 更新连接状态
        window.apiStatus.connected = true;
        window.apiStatus.lastUpdate = new Date();
        console.log('✅ 币安API连接成功');

        // 获取现货账户信息
        const spotSuccess = await fetchSpotAccountInfo();

        // 获取杠杆账户信息
        const marginSuccess = await fetchMarginAccountInfo();

        // 暂时禁用WebSocket连接，避免CORS错误
        // await initializePriceWebSocket();

        // 获取初始数据
        await fetchInitialData();

        // 更新UI显示
        updateConnectionStatus();

        // 强制更新余额显示
        if (window.updateBalanceDisplay) {
            window.updateBalanceDisplay();
        }

        return true;

    } catch (error) {
        console.error('❌ 币安API初始化失败:', error);
        window.apiStatus.connected = false;
        window.apiStatus.error = error.message;
        
        // 更新UI显示错误状态
        updateConnectionStatus();
        
        // 显示错误提示
        if (window.showNotification) {
            window.showNotification('API连接失败: ' + error.message, 'error');
        }
        
        return false;
    }
}

// 获取币安服务器时间
async function getBinanceServerTime() {
    try {
        console.log('📡 正在获取服务器时间...');
        const response = await fetch('http://localhost:3001/api/binance/time');
        const data = await response.json();
        
        console.log('⏰ 服务器时间API响应:', data);
        
        if (data.success && data.data) {
            const serverTime = data.data.serverTime || data.data;
            console.log('✅ 服务器时间获取成功:', new Date(serverTime));
            return serverTime;
        } else {
            console.error('❌ 服务器时间响应格式错误:', data);
            return null;
        }
    } catch (error) {
        console.error('❌ 获取服务器时间失败:', error);
        return null;
    }
}

// 获取现货账户信息
async function fetchSpotAccountInfo() {
    try {
        console.log('📡 正在获取现货账户信息...');
        const response = await fetch('http://localhost:3001/api/binance/account'); // 通过后端代理
        const data = await response.json();

        console.log('📊 现货账户API响应数据:', data);

        if (data.success) {
            // 处理币安API返回的数据结构
            const accountData = data.data || data;
            const balances = accountData.balances || [];

            // 计算总余额和可用余额
            let totalBalance = 0;
            let availableBalance = 0;

            // 遍历所有余额，计算USDT等值
            for (const balance of balances) {
                const free = parseFloat(balance.free || 0);
                const locked = parseFloat(balance.locked || 0);
                
                if (free > 0 || locked > 0) {
                    console.log(`💰 ${balance.asset}: 可用=${free}, 冻结=${locked}`);
                    
                    // 如果是USDT，直接计算
                    if (balance.asset === 'USDT') {
                        totalBalance += free + locked;
                        availableBalance += free;
                    }
                    // 其他币种需要转换为USDT价值（这里简化处理）
                    // 实际应用中需要获取实时汇率
                }
            }

            // 更新全局状态
            if (window.accountInfo) {
                window.accountInfo.spot = {
                    totalBalance: totalBalance,
                    availableBalance: availableBalance,
                    balances: balances,
                    canTrade: accountData.canTrade || false,
                    lastUpdate: new Date()
                };
            }

            console.log('✅ 现货账户信息更新成功');
            console.log(`💰 现货总余额: $${totalBalance.toFixed(2)}`);
            console.log(`💵 现货可用余额: $${availableBalance.toFixed(2)}`);

            return true;
        } else {
            console.error('❌ 现货账户信息获取失败:', data.message || '未知错误');
            return false;
        }
    } catch (error) {
        console.error('❌ 获取现货账户信息时发生错误:', error);
        return false;
    }
}

// 获取杠杆账户信息
async function fetchMarginAccountInfo() {
    try {
        console.log('📡 正在获取杠杆账户信息...');
        const response = await fetch('http://localhost:3001/api/binance/margin/account');
        const data = await response.json();

        console.log('📊 杠杆账户API响应数据:', data);

        if (data.success) {
            const accountData = data.data || data;
            
            // 解析杠杆账户数据
            const totalAssetOfBtc = parseFloat(accountData.totalAssetOfBtc || 0);
            const totalLiabilityOfBtc = parseFloat(accountData.totalLiabilityOfBtc || 0);
            const totalNetAssetOfBtc = parseFloat(accountData.totalNetAssetOfBtc || 0);
            
            // 更新全局状态
            if (window.accountInfo) {
                window.accountInfo.margin = {
                    totalAssetOfBtc: totalAssetOfBtc,
                    totalLiabilityOfBtc: totalLiabilityOfBtc,
                    totalNetAssetOfBtc: totalNetAssetOfBtc,
                    marginLevel: parseFloat(accountData.marginLevel || 0),
                    userAssets: accountData.userAssets || [],
                    lastUpdate: new Date()
                };
            }

            console.log('✅ 杠杆账户信息更新成功');
            console.log(`💰 杠杆净资产: ${totalNetAssetOfBtc.toFixed(8)} BTC`);

            return true;
        } else {
            console.error('❌ 杠杆账户信息获取失败:', data.message || '未知错误');
            return false;
        }
    } catch (error) {
        console.error('❌ 获取杠杆账户信息时发生错误:', error);
        return false;
    }
}

// 获取初始数据
async function fetchInitialData() {
    try {
        console.log('📊 正在获取初始市场数据...');
        
        // 获取24小时价格变动数据
        const response = await fetch('http://localhost:3001/api/binance/ticker/24hr');
        const data = await response.json();
        
        if (data.success && data.data) {
            console.log('✅ 市场数据获取成功');
            
            // 更新价格显示
            if (window.updatePriceDisplay) {
                window.updatePriceDisplay(data.data);
            }
        }
    } catch (error) {
        console.error('❌ 获取初始数据失败:', error);
    }
}

// 更新连接状态显示
function updateConnectionStatus() {
    const statusElement = document.getElementById('connection-status');
    const balanceElement = document.getElementById('current-balance');
    
    if (statusElement) {
        if (window.apiStatus.connected) {
            statusElement.textContent = '已连接';
            statusElement.className = 'status connected';
        } else {
            statusElement.textContent = '未连接';
            statusElement.className = 'status disconnected';
        }
    }
    
    // 更新余额显示
    if (balanceElement && window.accountInfo) {
        const spotBalance = window.accountInfo.spot?.totalBalance || 0;
        const marginBalance = window.accountInfo.margin?.totalNetAssetOfBtc || 0;
        balanceElement.textContent = `$${spotBalance.toFixed(2)}`;
    }
}

// 导出函数到全局作用域
window.BINANCE_CONFIG = BINANCE_CONFIG;
window.TELEGRAM_CONFIG = TELEGRAM_CONFIG;
window.initializeBinanceAPI = initializeBinanceAPI;
window.getBinanceServerTime = getBinanceServerTime;
window.fetchSpotAccountInfo = fetchSpotAccountInfo;
window.fetchMarginAccountInfo = fetchMarginAccountInfo;
window.fetchAccountInfo = fetchSpotAccountInfo; // 兼容旧代码
window.apiStatus = apiStatus;

console.log('🔗 币安API集成模块已加载');
